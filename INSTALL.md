# 安装指南

## 系统要求

### 基础环境
- **Python**: 3.8 或更高版本
- **Node.js**: 16 或更高版本
- **npm**: 7 或更高版本
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+

### 硬件要求
- **内存**: 最少 4GB RAM，推荐 8GB+
- **存储**: 至少 2GB 可用空间
- **摄像头**: 用于实时分析功能（可选）

## 快速开始

### 方法一：使用启动脚本（推荐）

#### Windows
```bash
# 双击运行或在命令行中执行
start.bat
```

#### Linux/macOS
```bash
# 给脚本执行权限
chmod +x start.sh

# 运行脚本
./start.sh
```

### 方法二：手动安装

#### 1. 克隆项目
```bash
git clone <repository-url>
cd FacialAnalysis
```

#### 2. 后端安装
```bash
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 启动后端服务
python main.py
```

#### 3. 前端安装
```bash
# 新开一个终端窗口
cd frontend

# 安装依赖
npm install

# 启动前端服务
npm run dev
```

## 访问系统

安装完成后，您可以通过以下地址访问系统：

- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 功能验证

### 1. 基础功能测试
1. 打开前端界面 (http://localhost:3000)
2. 导航到"图片分析"页面
3. 上传一张包含人脸的图片
4. 点击"开始分析"按钮
5. 查看分析结果

### 2. 实时分析测试
1. 导航到"实时分析"页面
2. 点击"启动摄像头"按钮
3. 允许浏览器访问摄像头
4. 点击"开始分析"按钮
5. 观察实时分析结果

## 常见问题

### Q: Python依赖安装失败
**A**: 确保您使用的是Python 3.8+版本，并且网络连接正常。如果仍有问题，尝试：
```bash
pip install --upgrade pip
pip install -r requirements.txt --no-cache-dir
```

### Q: 前端依赖安装失败
**A**: 确保Node.js版本为16+，尝试清除缓存：
```bash
npm cache clean --force
npm install
```

### Q: 摄像头无法访问
**A**: 
1. 确保浏览器有摄像头权限
2. 使用HTTPS或localhost访问
3. 检查其他应用是否占用摄像头

### Q: 端口被占用
**A**: 修改配置文件中的端口设置：
- 后端: 编辑 `backend/.env` 文件中的 `PORT` 值
- 前端: 编辑 `frontend/vite.config.ts` 文件中的端口设置

### Q: AI模型加载失败
**A**: 
1. 检查网络连接，某些模型需要首次下载
2. 确保有足够的磁盘空间
3. 如果问题持续，系统会使用默认模型

## 高级配置

### 环境变量配置
复制 `backend/.env.example` 为 `backend/.env` 并根据需要修改配置：

```bash
cp backend/.env.example backend/.env
```

主要配置项：
- `HOST`: 服务器地址
- `PORT`: 服务器端口
- `DEBUG`: 调试模式
- `FACE_DETECTION_CONFIDENCE`: 人脸检测置信度阈值
- `FACE_RECOGNITION_TOLERANCE`: 人脸识别容忍度

### 模型配置
系统支持多种AI模型，可在配置文件中切换：
- 人脸检测: MediaPipe, OpenCV, Dlib
- 表情识别: 自定义CNN模型
- 年龄性别预测: 自定义模型

## 开发模式

### 后端开发
```bash
cd backend
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 启动开发服务器（自动重载）
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 前端开发
```bash
cd frontend

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

## 部署

### 生产环境部署
1. 设置环境变量 `DEBUG=False`
2. 使用生产级WSGI服务器（如Gunicorn）
3. 配置反向代理（如Nginx）
4. 设置HTTPS证书

### Docker部署（可选）
项目支持Docker部署，详见 `docker-compose.yml` 文件。

## 技术支持

如果您在安装或使用过程中遇到问题：

1. 查看本文档的常见问题部分
2. 检查系统日志文件 `backend/logs/`
3. 提交Issue到项目仓库
4. 联系技术支持团队

## 更新系统

### 更新代码
```bash
git pull origin main
```

### 更新依赖
```bash
# 后端
cd backend
pip install -r requirements.txt --upgrade

# 前端
cd frontend
npm update
```

---

**注意**: 首次运行时，系统可能需要下载AI模型文件，请确保网络连接稳定。
