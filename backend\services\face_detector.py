"""
人脸检测服务
Face Detection Service
"""

import cv2
import numpy as np
import mediapipe as mp
from typing import List, Dict, Tuple, Optional
import time

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from utils.logger import app_logger
from utils.config import settings

class FaceDetector:
    """人脸检测器"""
    
    def __init__(self):
        """初始化人脸检测器"""
        self.logger = app_logger
        self.confidence_threshold = settings.FACE_DETECTION_CONFIDENCE
        
        # 初始化MediaPipe人脸检测
        self.mp_face_detection = mp.solutions.face_detection
        self.mp_drawing = mp.solutions.drawing_utils
        self.face_detection = self.mp_face_detection.FaceDetection(
            model_selection=1,  # 0: 近距离模型, 1: 远距离模型
            min_detection_confidence=self.confidence_threshold
        )
        
        # 初始化MediaPipe人脸网格（用于特征点检测）
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=10,
            refine_landmarks=True,
            min_detection_confidence=self.confidence_threshold,
            min_tracking_confidence=0.5
        )
        
        self.logger.info("人脸检测器初始化完成")
    
    def detect_faces(self, image: np.ndarray) -> List[Dict]:
        """
        检测图像中的人脸
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            检测结果列表，每个结果包含边界框、置信度等信息
        """
        start_time = time.time()
        
        try:
            # 转换为RGB格式
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            height, width = image.shape[:2]
            
            # 执行人脸检测
            results = self.face_detection.process(rgb_image)
            
            faces = []
            if results.detections:
                for i, detection in enumerate(results.detections):
                    # 获取边界框
                    bbox = detection.location_data.relative_bounding_box
                    
                    # 转换为像素坐标
                    x = int(bbox.xmin * width)
                    y = int(bbox.ymin * height)
                    w = int(bbox.width * width)
                    h = int(bbox.height * height)
                    
                    # 确保坐标在图像范围内
                    x = max(0, x)
                    y = max(0, y)
                    w = min(w, width - x)
                    h = min(h, height - y)
                    
                    face_info = {
                        'id': i,
                        'bbox': {
                            'x': x,
                            'y': y,
                            'width': w,
                            'height': h
                        },
                        'confidence': detection.score[0],
                        'center': {
                            'x': x + w // 2,
                            'y': y + h // 2
                        },
                        'area': w * h
                    }
                    
                    faces.append(face_info)
            
            processing_time = time.time() - start_time
            
            self.logger.info(f"检测到 {len(faces)} 张人脸，耗时 {processing_time:.3f}s")
            
            return faces
            
        except Exception as e:
            self.logger.error(f"人脸检测失败: {str(e)}")
            return []
    
    def detect_landmarks(self, image: np.ndarray) -> List[Dict]:
        """
        检测面部特征点
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            特征点检测结果列表
        """
        start_time = time.time()
        
        try:
            # 转换为RGB格式
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            height, width = image.shape[:2]
            
            # 执行面部网格检测
            results = self.face_mesh.process(rgb_image)
            
            faces_landmarks = []
            if results.multi_face_landmarks:
                for i, face_landmarks in enumerate(results.multi_face_landmarks):
                    landmarks = []
                    
                    # 提取关键特征点（68个点的子集）
                    key_points_indices = [
                        # 脸部轮廓 (17个点)
                        10, 338, 297, 332, 284, 251, 389, 356, 454, 323, 361, 288, 397, 365, 379, 378, 400,
                        # 左眉毛 (5个点)  
                        70, 63, 105, 66, 107,
                        # 右眉毛 (5个点)
                        55, 65, 52, 53, 46,
                        # 鼻子 (9个点)
                        1, 2, 5, 4, 6, 168, 8, 9, 10,
                        # 左眼 (6个点)
                        33, 7, 163, 144, 145, 153,
                        # 右眼 (6个点)
                        362, 382, 381, 380, 374, 373,
                        # 嘴巴 (20个点)
                        78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308, 415, 310, 311, 312, 13, 82, 81, 80, 78
                    ]
                    
                    for idx in key_points_indices:
                        if idx < len(face_landmarks.landmark):
                            landmark = face_landmarks.landmark[idx]
                            landmarks.append({
                                'x': int(landmark.x * width),
                                'y': int(landmark.y * height),
                                'z': landmark.z if hasattr(landmark, 'z') else 0
                            })
                    
                    face_landmarks_info = {
                        'id': i,
                        'landmarks': landmarks,
                        'total_points': len(landmarks)
                    }
                    
                    faces_landmarks.append(face_landmarks_info)
            
            processing_time = time.time() - start_time
            
            self.logger.info(f"检测到 {len(faces_landmarks)} 张人脸的特征点，耗时 {processing_time:.3f}s")
            
            return faces_landmarks
            
        except Exception as e:
            self.logger.error(f"特征点检测失败: {str(e)}")
            return []
    
    def crop_faces(self, image: np.ndarray, faces: List[Dict], padding: float = 0.2) -> List[np.ndarray]:
        """
        从图像中裁剪人脸区域
        
        Args:
            image: 原始图像
            faces: 人脸检测结果
            padding: 裁剪时的边距比例
            
        Returns:
            裁剪后的人脸图像列表
        """
        cropped_faces = []
        
        for face in faces:
            try:
                bbox = face['bbox']
                x, y, w, h = bbox['x'], bbox['y'], bbox['width'], bbox['height']
                
                # 添加边距
                pad_w = int(w * padding)
                pad_h = int(h * padding)
                
                # 计算裁剪区域
                x1 = max(0, x - pad_w)
                y1 = max(0, y - pad_h)
                x2 = min(image.shape[1], x + w + pad_w)
                y2 = min(image.shape[0], y + h + pad_h)
                
                # 裁剪人脸
                cropped_face = image[y1:y2, x1:x2]
                
                if cropped_face.size > 0:
                    cropped_faces.append(cropped_face)
                
            except Exception as e:
                self.logger.error(f"裁剪人脸失败: {str(e)}")
                continue
        
        return cropped_faces
    
    def draw_detections(self, image: np.ndarray, faces: List[Dict], 
                       landmarks: Optional[List[Dict]] = None) -> np.ndarray:
        """
        在图像上绘制检测结果
        
        Args:
            image: 原始图像
            faces: 人脸检测结果
            landmarks: 特征点检测结果（可选）
            
        Returns:
            绘制了检测结果的图像
        """
        result_image = image.copy()
        
        # 绘制人脸边界框
        for face in faces:
            bbox = face['bbox']
            confidence = face['confidence']
            
            # 绘制边界框
            cv2.rectangle(
                result_image,
                (bbox['x'], bbox['y']),
                (bbox['x'] + bbox['width'], bbox['y'] + bbox['height']),
                (0, 255, 0), 2
            )
            
            # 绘制置信度
            cv2.putText(
                result_image,
                f"Face: {confidence:.2f}",
                (bbox['x'], bbox['y'] - 10),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.5, (0, 255, 0), 1
            )
        
        # 绘制特征点
        if landmarks:
            for face_landmarks in landmarks:
                for point in face_landmarks['landmarks']:
                    cv2.circle(result_image, (point['x'], point['y']), 2, (255, 0, 0), -1)
        
        return result_image
