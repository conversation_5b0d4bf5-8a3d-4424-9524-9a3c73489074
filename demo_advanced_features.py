#!/usr/bin/env python3
"""
高级功能演示脚本
Advanced Features Demo Script
"""

import requests
import json
import time
import os
from pathlib import Path
import cv2
import numpy as np

def create_demo_image():
    """创建演示用的人脸图像"""
    try:
        # 创建一个更复杂的测试图片
        img = np.zeros((400, 400, 3), dtype=np.uint8)
        img.fill(240)  # 浅灰色背景
        
        # 绘制人脸轮廓
        cv2.ellipse(img, (200, 200), (120, 150), 0, 0, 360, (220, 200, 180), -1)
        
        # 绘制眼睛
        cv2.ellipse(img, (170, 170), (25, 15), 0, 0, 360, (255, 255, 255), -1)
        cv2.ellipse(img, (230, 170), (25, 15), 0, 0, 360, (255, 255, 255), -1)
        cv2.circle(img, (170, 170), 8, (0, 0, 0), -1)
        cv2.circle(img, (230, 170), 8, (0, 0, 0), -1)
        
        # 绘制眉毛
        cv2.ellipse(img, (170, 150), (30, 8), 0, 0, 180, (100, 80, 60), -1)
        cv2.ellipse(img, (230, 150), (30, 8), 0, 0, 180, (100, 80, 60), -1)
        
        # 绘制鼻子
        cv2.ellipse(img, (200, 200), (8, 20), 0, 0, 360, (200, 180, 160), -1)
        
        # 绘制嘴巴
        cv2.ellipse(img, (200, 240), (40, 20), 0, 0, 180, (180, 100, 100), -1)
        
        # 添加一些纹理
        noise = np.random.randint(-10, 10, img.shape, dtype=np.int16)
        img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        
        demo_image_path = "demo_face.jpg"
        cv2.imwrite(demo_image_path, img)
        print(f"✅ 创建演示图片: {demo_image_path}")
        return demo_image_path
    except Exception as e:
        print(f"❌ 创建演示图片失败: {e}")
        return None

def test_comprehensive_analysis(image_path):
    """测试综合面部分析功能"""
    print("\n🧠 测试综合面部分析功能...")
    try:
        with open(image_path, 'rb') as f:
            files = {'file': ('demo_face.jpg', f, 'image/jpeg')}
            response = requests.post(
                "http://localhost:8000/api/v1/advanced/comprehensive-analysis",
                files=files,
                timeout=30
            )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                results = data.get('results', {})
                advanced_analysis = results.get('advanced_analysis', [])
                
                print(f"✅ 综合分析成功!")
                print(f"✅ 检测到人脸数: {results.get('basic_detection', {}).get('faces', []).__len__()}")
                print(f"✅ 处理时间: {data.get('processing_time', 0):.2f}秒")
                
                if advanced_analysis:
                    analysis = advanced_analysis[0]
                    
                    # 对称性分析
                    if 'symmetry' in analysis:
                        symmetry = analysis['symmetry']
                        print(f"✅ 面部对称度: {symmetry.get('symmetry_score', 0)*100:.1f}%")
                        print(f"✅ 对称性评价: {symmetry.get('analysis', '未知')}")
                    
                    # 面部形状
                    if 'face_shape' in analysis:
                        face_shape = analysis['face_shape']
                        print(f"✅ 面部形状: {face_shape.get('face_shape', '未知')}")
                        print(f"✅ 形状置信度: {face_shape.get('confidence', 0)*100:.1f}%")
                    
                    # 肤色分析
                    if 'skin_tone' in analysis:
                        skin_tone = analysis['skin_tone']
                        print(f"✅ 肤色类型: {skin_tone.get('skin_tone', '未知')}")
                        print(f"✅ 肤色代码: {skin_tone.get('hex_color', '#000000')}")
                
                return True
            else:
                print(f"❌ 综合分析失败: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ 综合分析请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 综合分析测试失败: {e}")
        return False

def test_beauty_filter(image_path):
    """测试美颜滤镜功能"""
    print("\n✨ 测试美颜滤镜功能...")
    try:
        beauty_settings = {
            "skin_smoothing": 0.7,
            "brightness": 20,
            "contrast": 1.3,
            "saturation": 1.4,
            "eye_enhancement": 0.5,
            "teeth_whitening": 0.4,
            "blemish_removal": 0.6
        }
        
        with open(image_path, 'rb') as f:
            files = {'file': ('demo_face.jpg', f, 'image/jpeg')}
            data = {'settings': json.dumps(beauty_settings)}
            response = requests.post(
                "http://localhost:8000/api/v1/advanced/beauty-filter",
                files=files,
                data=data,
                timeout=30
            )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 美颜滤镜应用成功!")
                print(f"✅ 检测到人脸数: {result.get('faces_detected', 0)}")
                print(f"✅ 处理时间: {result.get('processing_time', 0):.2f}秒")
                
                settings_applied = result.get('settings_applied', {})
                print(f"✅ 应用的美颜设置:")
                for key, value in settings_applied.items():
                    print(f"   - {key}: {value}")
                
                # 检查是否有图像数据
                images = result.get('images', {})
                if 'enhanced' in images:
                    print(f"✅ 美颜后图像已生成")
                
                return True
            else:
                print(f"❌ 美颜滤镜失败: {result.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ 美颜滤镜请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 美颜滤镜测试失败: {e}")
        return False

def test_analytics_endpoints():
    """测试数据分析端点"""
    print("\n📊 测试数据分析功能...")
    
    endpoints = [
        ("每日统计", "/api/v1/analytics/daily-stats?days=7"),
        ("表情统计", "/api/v1/analytics/emotion-stats?days=30"),
        ("人口统计", "/api/v1/analytics/demographic-stats?days=30"),
        ("性能统计", "/api/v1/analytics/performance-stats?days=7"),
        ("综合报告", "/api/v1/analytics/comprehensive-report?days=30")
    ]
    
    results = []
    
    for name, endpoint in endpoints:
        try:
            response = requests.get(f"http://localhost:8000{endpoint}", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✅ {name}: 数据获取成功")
                    results.append(True)
                else:
                    print(f"⚠️  {name}: 暂无数据")
                    results.append(True)  # 无数据也算正常
            else:
                print(f"❌ {name}: HTTP {response.status_code}")
                results.append(False)
        except Exception as e:
            print(f"❌ {name}: {e}")
            results.append(False)
    
    return all(results)

def test_face_comparison():
    """测试人脸比对功能"""
    print("\n🔍 测试人脸比对功能...")
    
    # 创建两张相似的测试图片
    try:
        # 第一张图片
        img1 = np.zeros((300, 300, 3), dtype=np.uint8)
        img1.fill(240)
        cv2.ellipse(img1, (150, 150), (80, 100), 0, 0, 360, (220, 200, 180), -1)
        cv2.circle(img1, (130, 130), 8, (0, 0, 0), -1)
        cv2.circle(img1, (170, 130), 8, (0, 0, 0), -1)
        cv2.ellipse(img1, (150, 170), (25, 12), 0, 0, 180, (180, 100, 100), -1)
        cv2.imwrite("face1.jpg", img1)
        
        # 第二张图片（稍有不同）
        img2 = np.zeros((300, 300, 3), dtype=np.uint8)
        img2.fill(235)
        cv2.ellipse(img2, (150, 150), (85, 105), 0, 0, 360, (215, 195, 175), -1)
        cv2.circle(img2, (125, 135), 9, (0, 0, 0), -1)
        cv2.circle(img2, (175, 135), 9, (0, 0, 0), -1)
        cv2.ellipse(img2, (150, 175), (28, 15), 0, 0, 180, (175, 95, 95), -1)
        cv2.imwrite("face2.jpg", img2)
        
        # 进行比对
        with open("face1.jpg", 'rb') as f1, open("face2.jpg", 'rb') as f2:
            files = {
                'file1': ('face1.jpg', f1, 'image/jpeg'),
                'file2': ('face2.jpg', f2, 'image/jpeg')
            }
            response = requests.post(
                "http://localhost:8000/api/v1/advanced/face-comparison",
                files=files,
                timeout=30
            )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                comparison_results = result.get('results', {})
                similarity = comparison_results.get('similarity_percentage', 0)
                is_same = comparison_results.get('is_same_person', False)
                confidence = comparison_results.get('confidence', 'unknown')
                
                print(f"✅ 人脸比对完成!")
                print(f"✅ 相似度: {similarity:.2f}%")
                print(f"✅ 是否同一人: {'是' if is_same else '否'}")
                print(f"✅ 置信度: {confidence}")
                print(f"✅ 处理时间: {result.get('processing_time', 0):.2f}秒")
                
                # 清理测试文件
                os.remove("face1.jpg")
                os.remove("face2.jpg")
                
                return True
            else:
                print(f"❌ 人脸比对失败: {result.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ 人脸比对请求失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 人脸比对测试失败: {e}")
        # 清理可能存在的测试文件
        for file in ["face1.jpg", "face2.jpg"]:
            if os.path.exists(file):
                os.remove(file)
        return False

def cleanup_demo_files():
    """清理演示文件"""
    demo_files = ["demo_face.jpg", "face1.jpg", "face2.jpg"]
    for file in demo_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🧹 清理演示文件: {file}")

def main():
    """主演示函数"""
    print("🚀 高级功能演示开始")
    print("=" * 60)
    
    # 创建演示图片
    demo_image = create_demo_image()
    if not demo_image:
        print("❌ 无法创建演示图片，退出演示")
        return 1
    
    test_results = []
    
    # 测试各项高级功能
    test_results.append(("综合面部分析", test_comprehensive_analysis(demo_image)))
    test_results.append(("美颜滤镜", test_beauty_filter(demo_image)))
    test_results.append(("人脸比对", test_face_comparison()))
    test_results.append(("数据分析", test_analytics_endpoints()))
    
    # 清理演示文件
    cleanup_demo_files()
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 高级功能演示结果:")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"总计: {passed}/{total} 项高级功能测试通过")
    
    if passed == total:
        print("🎉 所有高级功能测试通过！系统功能完善。")
        print("\n🌟 新增功能包括:")
        print("   • 综合面部分析 (对称性、形状、肤色、特征)")
        print("   • 美颜滤镜 (皮肤平滑、亮度调整、眼部增强等)")
        print("   • 人脸比对 (相似度计算)")
        print("   • 数据分析 (使用统计、性能监控)")
        print("   • 高级图像处理 (多种滤镜效果)")
        return 0
    else:
        print("⚠️  部分高级功能测试失败，请检查系统配置。")
        return 1

if __name__ == "__main__":
    exit(main())
