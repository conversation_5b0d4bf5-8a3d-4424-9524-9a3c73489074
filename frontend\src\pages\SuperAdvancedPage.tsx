import React, { useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { useDropzone } from 'react-dropzone'
import { 
  Upload, 
  <PERSON>rkles, 
  BarChart3, 
  Zap, 
  Eye, 
  Palette,
  Loader2,
  Download,
  GitCompare,
  Brain,
  Cube,
  Heart,
  Lightbulb,
  Camera,
  Wand2
} from 'lucide-react'
import toast from 'react-hot-toast'

const SuperAdvancedPage = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [analysisResult, setAnalysisResult] = useState<any>(null)
  const [activeTab, setActiveTab] = useState('3d')

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (file) {
      setSelectedFile(file)
      const url = URL.createObjectURL(file)
      setPreviewUrl(url)
      setAnalysisResult(null)
      toast.success('图片上传成功！')
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.bmp', '.tiff']
    },
    multiple: false,
    maxSize: 10 * 1024 * 1024
  })

  const handle3DAnalysis = async () => {
    if (!selectedFile) {
      toast.error('请先选择一张图片')
      return
    }

    setIsProcessing(true)
    
    try {
      const formData = new FormData()
      formData.append('file', selectedFile)

      const response = await fetch('/api/v1/advanced/3d-analysis', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error('3D分析失败')
      }

      const result = await response.json()
      setAnalysisResult(result)
      toast.success('3D分析完成！')
      
    } catch (error) {
      console.error('3D Analysis error:', error)
      toast.error('3D分析失败，请重试')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleEmotionAI = async () => {
    if (!selectedFile) {
      toast.error('请先选择一张图片')
      return
    }

    setIsProcessing(true)
    
    try {
      const formData = new FormData()
      formData.append('file', selectedFile)

      const response = await fetch('/api/v1/advanced/emotion-ai', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error('情感AI分析失败')
      }

      const result = await response.json()
      setAnalysisResult(result)
      toast.success('情感AI分析完成！')
      
    } catch (error) {
      console.error('Emotion AI error:', error)
      toast.error('情感AI分析失败，请重试')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSmartRecommendations = async () => {
    if (!selectedFile) {
      toast.error('请先选择一张图片')
      return
    }

    setIsProcessing(true)
    
    try {
      const formData = new FormData()
      formData.append('file', selectedFile)

      const response = await fetch('/api/v1/advanced/smart-recommendations', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error('智能推荐失败')
      }

      const result = await response.json()
      setAnalysisResult(result)
      toast.success('智能推荐完成！')
      
    } catch (error) {
      console.error('Smart recommendations error:', error)
      toast.error('智能推荐失败，请重试')
    } finally {
      setIsProcessing(false)
    }
  }

  const tabs = [
    { id: '3d', label: '3D重建', icon: Cube, action: handle3DAnalysis, description: '头部姿态、深度图、3D线框' },
    { id: 'emotion', label: '情感AI', icon: Heart, action: handleEmotionAI, description: '微表情、情感强度分析' },
    { id: 'recommendations', label: '智能推荐', icon: Lightbulb, action: handleSmartRecommendations, description: '美颜、拍照、化妆建议' }
  ]

  const render3DResults = () => {
    if (!analysisResult?.results) return null

    const results = analysisResult.results
    
    return (
      <div className="space-y-6">
        {/* 头部姿态 */}
        {results.pose_estimation?.success && (
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">头部姿态分析</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {results.pose_estimation.euler_angles?.pitch?.toFixed(1)}°
                </div>
                <div className="text-sm text-blue-700">俯仰角 (Pitch)</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {results.pose_estimation.euler_angles?.yaw?.toFixed(1)}°
                </div>
                <div className="text-sm text-green-700">偏航角 (Yaw)</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {results.pose_estimation.euler_angles?.roll?.toFixed(1)}°
                </div>
                <div className="text-sm text-purple-700">翻滚角 (Roll)</div>
              </div>
            </div>
            <div className="mt-4 p-4 bg-slate-50 rounded-lg">
              <p className="text-slate-700">
                <span className="font-medium">姿态描述:</span> {results.pose_estimation.pose_description}
              </p>
            </div>
          </div>
        )}

        {/* 3D可视化结果 */}
        {results.images && (
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">3D可视化</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {results.images.depth_map && (
                <div>
                  <h4 className="text-sm font-medium text-slate-700 mb-2">深度图</h4>
                  <img
                    src={results.images.depth_map}
                    alt="Depth Map"
                    className="w-full rounded-lg shadow-md"
                  />
                  {results.depth_statistics && (
                    <div className="mt-2 text-xs text-slate-600">
                      深度范围: {results.depth_statistics.min_depth?.toFixed(1)} - {results.depth_statistics.max_depth?.toFixed(1)}
                    </div>
                  )}
                </div>
              )}
              
              {results.images.wireframe && (
                <div>
                  <h4 className="text-sm font-medium text-slate-700 mb-2">3D线框</h4>
                  <img
                    src={results.images.wireframe}
                    alt="3D Wireframe"
                    className="w-full rounded-lg shadow-md"
                  />
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    )
  }

  const renderEmotionResults = () => {
    if (!analysisResult?.results) return null

    const results = analysisResult.results
    
    return (
      <div className="space-y-6">
        {/* 情感强度 */}
        {results.emotion_intensity?.success && (
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">情感强度分析</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-pink-50 rounded-lg">
                <div className="text-xl font-bold text-pink-600">
                  {results.emotion_intensity.emotion_cn}
                </div>
                <div className="text-sm text-pink-700">主要情感</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">
                  {(results.emotion_intensity.adjusted_confidence * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-orange-700">调整后置信度</div>
              </div>
              <div className="text-center p-4 bg-indigo-50 rounded-lg">
                <div className="text-lg font-semibold text-indigo-600">
                  {results.emotion_intensity.intensity_level}
                </div>
                <div className="text-sm text-indigo-700">强度等级</div>
              </div>
            </div>
          </div>
        )}

        {/* 微表情分析 */}
        {results.micro_expressions?.success && (
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">微表情分析</h3>
            <div className="space-y-4">
              {results.micro_expressions.micro_expressions?.map((expr: any, index: number) => (
                <div key={index} className="p-4 bg-slate-50 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium text-slate-900">{expr.region}</span>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      expr.intensity === 'high' ? 'bg-red-100 text-red-700' :
                      expr.intensity === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                      'bg-green-100 text-green-700'
                    }`}>
                      {expr.intensity}
                    </span>
                  </div>
                  <p className="text-slate-600">{expr.expression}</p>
                  {expr.ratio && (
                    <p className="text-xs text-slate-500 mt-1">比例: {expr.ratio}</p>
                  )}
                </div>
              ))}
            </div>
            
            {results.micro_expressions.interpretation && (
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <p className="text-blue-800">
                  <span className="font-medium">综合解释:</span> {results.micro_expressions.interpretation}
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    )
  }

  const renderRecommendationResults = () => {
    if (!analysisResult?.results) return null

    const results = analysisResult.results
    
    return (
      <div className="space-y-6">
        {/* 美颜设置推荐 */}
        {results.beauty_settings?.success && (
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">美颜设置推荐</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              {Object.entries(results.beauty_settings.recommended_settings).map(([key, value]: [string, any]) => (
                <div key={key} className="text-center p-3 bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg">
                  <div className="text-lg font-bold text-purple-600">
                    {typeof value === 'number' ? value.toFixed(2) : value}
                  </div>
                  <div className="text-xs text-purple-700">{key}</div>
                </div>
              ))}
            </div>
            
            {results.beauty_settings.recommendations && (
              <div className="space-y-2">
                <h4 className="font-medium text-slate-700">推荐依据:</h4>
                {results.beauty_settings.recommendations.map((rec: string, index: number) => (
                  <div key={index} className="flex items-center text-sm text-slate-600">
                    <Sparkles className="w-4 h-4 mr-2 text-purple-500" />
                    {rec}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* 拍照建议 */}
        {results.photo_improvements?.success && (
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">拍照改进建议</h3>
            <div className="space-y-3">
              {results.photo_improvements.suggestions?.map((suggestion: any, index: number) => (
                <div key={index} className="flex items-start p-3 bg-slate-50 rounded-lg">
                  <Camera className={`w-5 h-5 mr-3 mt-0.5 ${
                    suggestion.priority === 'high' ? 'text-red-500' :
                    suggestion.priority === 'medium' ? 'text-yellow-500' :
                    'text-green-500'
                  }`} />
                  <div>
                    <div className="font-medium text-slate-900">{suggestion.category}</div>
                    <div className="text-sm text-slate-600">{suggestion.suggestion}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 化妆风格推荐 */}
        {results.makeup_style?.success && (
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">化妆风格推荐</h3>
            <div className="space-y-4">
              {results.makeup_style.makeup_recommendations?.map((rec: any, index: number) => (
                <div key={index} className="p-4 border border-slate-200 rounded-lg">
                  <div className="flex items-center mb-2">
                    <Wand2 className="w-5 h-5 mr-2 text-pink-500" />
                    <span className="font-medium text-slate-900">{rec.category}</span>
                  </div>
                  
                  {rec.recommended_style && (
                    <div className="mb-2">
                      <span className="text-sm font-medium text-slate-700">推荐风格: </span>
                      <span className="text-sm text-slate-600">{rec.recommended_style}</span>
                    </div>
                  )}
                  
                  {rec.tips && (
                    <div className="mb-2">
                      <span className="text-sm font-medium text-slate-700">技巧: </span>
                      <div className="text-sm text-slate-600">
                        {rec.tips.map((tip: string, tipIndex: number) => (
                          <div key={tipIndex}>• {tip}</div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {rec.recommended_colors && (
                    <div>
                      <span className="text-sm font-medium text-slate-700">推荐色彩: </span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {rec.recommended_colors.map((color: string, colorIndex: number) => (
                          <span key={colorIndex} className="px-2 py-1 bg-pink-100 text-pink-700 text-xs rounded">
                            {color}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h1 className="text-3xl font-bold text-slate-900 mb-4">
          超级高级功能
        </h1>
        <p className="text-lg text-slate-600">
          体验最前沿的AI技术：3D重建、情感AI、智能推荐
        </p>
      </motion.div>

      {/* 功能选项卡 */}
      <div className="card p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`p-4 rounded-lg text-left transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-br from-primary-500 to-primary-600 text-white shadow-lg'
                    : 'bg-slate-50 hover:bg-slate-100 text-slate-700'
                }`}
              >
                <div className="flex items-center mb-2">
                  <Icon className="w-6 h-6 mr-3" />
                  <span className="font-semibold">{tab.label}</span>
                </div>
                <p className="text-sm opacity-90">{tab.description}</p>
              </button>
            )
          })}
        </div>

        {/* 图片上传区域 */}
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200 mb-6 ${
            isDragActive
              ? 'border-primary-500 bg-primary-50'
              : 'border-slate-300 hover:border-primary-400 hover:bg-slate-50'
          }`}
        >
          <input {...getInputProps()} />
          <div className="space-y-4">
            <div className="mx-auto w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center">
              <Upload className="w-8 h-8 text-slate-400" />
            </div>
            <div>
              <p className="text-lg font-medium text-slate-900">
                {isDragActive ? '释放文件到这里' : '上传图片进行超级高级分析'}
              </p>
              <p className="text-sm text-slate-500 mt-1">
                支持 JPG, PNG, BMP, TIFF 格式，最大10MB
              </p>
            </div>
          </div>
        </div>

        {/* 图片预览和操作按钮 */}
        {previewUrl && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-slate-900 mb-4">图片预览</h3>
              <img
                src={previewUrl}
                alt="Preview"
                className="w-full h-64 object-cover rounded-lg shadow-md"
              />
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-slate-900 mb-4">开始分析</h3>
              <div className="space-y-3">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={tab.action}
                      disabled={isProcessing}
                      className="btn-primary w-full py-3 flex items-center justify-center"
                    >
                      {isProcessing ? (
                        <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                      ) : (
                        <Icon className="w-5 h-5 mr-2" />
                      )}
                      {tab.label}
                    </button>
                  )
                })}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 分析结果 */}
      {analysisResult && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* 处理统计 */}
          <div className="card p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-slate-900">处理结果</h3>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {analysisResult.faces_detected || 0}
                </div>
                <div className="text-sm text-blue-700">检测到的人脸</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {analysisResult.processing_time?.toFixed(2) || 0}s
                </div>
                <div className="text-sm text-green-700">处理时间</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {analysisResult.success ? '成功' : '失败'}
                </div>
                <div className="text-sm text-purple-700">处理状态</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">
                  {activeTab === '3d' ? '3D' : activeTab === 'emotion' ? '情感' : '推荐'}
                </div>
                <div className="text-sm text-orange-700">分析类型</div>
              </div>
            </div>
          </div>

          {/* 具体结果 */}
          {activeTab === '3d' && render3DResults()}
          {activeTab === 'emotion' && renderEmotionResults()}
          {activeTab === 'recommendations' && renderRecommendationResults()}
        </motion.div>
      )}
    </div>
  )
}

export default SuperAdvancedPage
