#!/usr/bin/env python3
"""
超级高级功能演示脚本
Super Advanced Features Demo Script
"""

import requests
import json
import time
import os
from pathlib import Path
import cv2
import numpy as np
import base64

def create_realistic_demo_image():
    """创建更真实的演示用人脸图像"""
    try:
        # 创建一个更复杂和真实的测试图片
        img = np.zeros((500, 400, 3), dtype=np.uint8)
        img.fill(245)  # 浅色背景
        
        # 绘制更真实的人脸
        center = (200, 250)
        
        # 面部轮廓 (椭圆)
        cv2.ellipse(img, center, (140, 180), 0, 0, 360, (220, 190, 170), -1)
        
        # 添加阴影效果
        cv2.ellipse(img, (center[0] + 10, center[1] + 10), (135, 175), 0, 0, 360, (200, 170, 150), -1)
        cv2.ellipse(img, center, (140, 180), 0, 0, 360, (230, 200, 180), -1)
        
        # 眼睛区域
        left_eye_center = (center[0] - 40, center[1] - 40)
        right_eye_center = (center[0] + 40, center[1] - 40)
        
        # 眼窝
        cv2.ellipse(img, left_eye_center, (35, 20), 0, 0, 360, (210, 180, 160), -1)
        cv2.ellipse(img, right_eye_center, (35, 20), 0, 0, 360, (210, 180, 160), -1)
        
        # 眼球
        cv2.ellipse(img, left_eye_center, (25, 15), 0, 0, 360, (255, 255, 255), -1)
        cv2.ellipse(img, right_eye_center, (25, 15), 0, 0, 360, (255, 255, 255), -1)
        
        # 瞳孔
        cv2.circle(img, left_eye_center, 8, (50, 50, 50), -1)
        cv2.circle(img, right_eye_center, 8, (50, 50, 50), -1)
        
        # 高光
        cv2.circle(img, (left_eye_center[0] - 3, left_eye_center[1] - 3), 3, (255, 255, 255), -1)
        cv2.circle(img, (right_eye_center[0] - 3, right_eye_center[1] - 3), 3, (255, 255, 255), -1)
        
        # 眉毛
        eyebrow_points_left = np.array([
            [left_eye_center[0] - 30, left_eye_center[1] - 25],
            [left_eye_center[0] - 10, left_eye_center[1] - 30],
            [left_eye_center[0] + 10, left_eye_center[1] - 25]
        ], np.int32)
        
        eyebrow_points_right = np.array([
            [right_eye_center[0] - 10, right_eye_center[1] - 25],
            [right_eye_center[0] + 10, right_eye_center[1] - 30],
            [right_eye_center[0] + 30, right_eye_center[1] - 25]
        ], np.int32)
        
        cv2.fillPoly(img, [eyebrow_points_left], (120, 90, 70))
        cv2.fillPoly(img, [eyebrow_points_right], (120, 90, 70))
        
        # 鼻子
        nose_points = np.array([
            [center[0], center[1] - 10],
            [center[0] - 8, center[1] + 20],
            [center[0] + 8, center[1] + 20]
        ], np.int32)
        cv2.fillPoly(img, [nose_points], (210, 180, 160))
        
        # 鼻孔
        cv2.ellipse(img, (center[0] - 6, center[1] + 15), (3, 6), 0, 0, 360, (180, 150, 130), -1)
        cv2.ellipse(img, (center[0] + 6, center[1] + 15), (3, 6), 0, 0, 360, (180, 150, 130), -1)
        
        # 嘴巴
        mouth_center = (center[0], center[1] + 60)
        cv2.ellipse(img, mouth_center, (30, 12), 0, 0, 180, (180, 100, 100), -1)
        cv2.ellipse(img, mouth_center, (25, 8), 0, 0, 180, (200, 120, 120), -1)
        
        # 添加一些自然的纹理和噪声
        noise = np.random.randint(-15, 15, img.shape, dtype=np.int16)
        img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        
        # 应用轻微的高斯模糊使其更自然
        img = cv2.GaussianBlur(img, (3, 3), 0)
        
        demo_image_path = "realistic_demo_face.jpg"
        cv2.imwrite(demo_image_path, img)
        print(f"✅ 创建真实演示图片: {demo_image_path}")
        return demo_image_path
    except Exception as e:
        print(f"❌ 创建演示图片失败: {e}")
        return None

def test_3d_analysis(image_path):
    """测试3D人脸分析功能"""
    print("\n🧊 测试3D人脸分析功能...")
    try:
        with open(image_path, 'rb') as f:
            files = {'file': ('realistic_demo_face.jpg', f, 'image/jpeg')}
            response = requests.post(
                "http://localhost:8000/api/v1/advanced/3d-analysis",
                files=files,
                timeout=30
            )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                results = data.get('results', {})
                
                print(f"✅ 3D分析成功!")
                print(f"✅ 检测到人脸数: {data.get('faces_detected', 0)}")
                print(f"✅ 处理时间: {data.get('processing_time', 0):.2f}秒")
                
                # 头部姿态分析
                pose = results.get('pose_estimation', {})
                if pose.get('success'):
                    angles = pose.get('euler_angles', {})
                    print(f"✅ 头部姿态:")
                    print(f"   - 俯仰角: {angles.get('pitch', 0):.1f}°")
                    print(f"   - 偏航角: {angles.get('yaw', 0):.1f}°")
                    print(f"   - 翻滚角: {angles.get('roll', 0):.1f}°")
                    print(f"   - 姿态描述: {pose.get('pose_description', '未知')}")
                
                # 深度图统计
                depth_stats = results.get('depth_statistics', {})
                if depth_stats:
                    print(f"✅ 深度图统计:")
                    print(f"   - 最小深度: {depth_stats.get('min_depth', 0):.1f}")
                    print(f"   - 最大深度: {depth_stats.get('max_depth', 0):.1f}")
                    print(f"   - 平均深度: {depth_stats.get('mean_depth', 0):.1f}")
                
                # 检查图像结果
                images = results.get('images', {})
                if images.get('depth_map'):
                    print(f"✅ 深度图已生成")
                if images.get('wireframe'):
                    print(f"✅ 3D线框已生成")
                
                return True
            else:
                print(f"❌ 3D分析失败: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ 3D分析请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 3D分析测试失败: {e}")
        return False

def test_emotion_ai(image_path):
    """测试情感AI分析功能"""
    print("\n❤️ 测试情感AI分析功能...")
    try:
        with open(image_path, 'rb') as f:
            files = {'file': ('realistic_demo_face.jpg', f, 'image/jpeg')}
            response = requests.post(
                "http://localhost:8000/api/v1/advanced/emotion-ai",
                files=files,
                timeout=30
            )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                results = data.get('results', {})
                
                print(f"✅ 情感AI分析成功!")
                print(f"✅ 检测到人脸数: {data.get('faces_detected', 0)}")
                print(f"✅ 处理时间: {data.get('processing_time', 0):.2f}秒")
                
                # 情感强度分析
                emotion_intensity = results.get('emotion_intensity', {})
                if emotion_intensity.get('success'):
                    print(f"✅ 情感强度分析:")
                    print(f"   - 主要情感: {emotion_intensity.get('emotion_cn', '未知')}")
                    print(f"   - 调整后置信度: {emotion_intensity.get('adjusted_confidence', 0)*100:.1f}%")
                    print(f"   - 强度等级: {emotion_intensity.get('intensity_level', '未知')}")
                    print(f"   - 情感倾向: {emotion_intensity.get('valence', '中性')}")
                
                # 微表情分析
                micro_expressions = results.get('micro_expressions', {})
                if micro_expressions.get('success'):
                    print(f"✅ 微表情分析:")
                    micro_exprs = micro_expressions.get('micro_expressions', [])
                    for expr in micro_exprs:
                        print(f"   - {expr.get('region', '未知区域')}: {expr.get('expression', '未知')} (强度: {expr.get('intensity', '未知')})")
                    
                    interpretation = micro_expressions.get('interpretation', '')
                    if interpretation:
                        print(f"   - 综合解释: {interpretation}")
                
                return True
            else:
                print(f"❌ 情感AI分析失败: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ 情感AI分析请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 情感AI分析测试失败: {e}")
        return False

def test_smart_recommendations(image_path):
    """测试智能推荐功能"""
    print("\n💡 测试智能推荐功能...")
    try:
        with open(image_path, 'rb') as f:
            files = {'file': ('realistic_demo_face.jpg', f, 'image/jpeg')}
            response = requests.post(
                "http://localhost:8000/api/v1/advanced/smart-recommendations",
                files=files,
                timeout=30
            )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                results = data.get('results', {})
                
                print(f"✅ 智能推荐成功!")
                print(f"✅ 检测到人脸数: {data.get('faces_detected', 0)}")
                print(f"✅ 处理时间: {data.get('processing_time', 0):.2f}秒")
                
                # 美颜设置推荐
                beauty_settings = results.get('beauty_settings', {})
                if beauty_settings.get('success'):
                    print(f"✅ 美颜设置推荐:")
                    settings = beauty_settings.get('recommended_settings', {})
                    for key, value in settings.items():
                        print(f"   - {key}: {value}")
                    
                    recommendations = beauty_settings.get('recommendations', [])
                    if recommendations:
                        print(f"   推荐依据: {', '.join(recommendations)}")
                
                # 拍照改进建议
                photo_improvements = results.get('photo_improvements', {})
                if photo_improvements.get('success'):
                    print(f"✅ 拍照改进建议:")
                    suggestions = photo_improvements.get('suggestions', [])
                    for suggestion in suggestions:
                        priority = suggestion.get('priority', 'medium')
                        category = suggestion.get('category', '未知')
                        content = suggestion.get('suggestion', '无建议')
                        print(f"   - [{priority.upper()}] {category}: {content}")
                
                # 化妆风格推荐
                makeup_style = results.get('makeup_style', {})
                if makeup_style.get('success'):
                    print(f"✅ 化妆风格推荐:")
                    recommendations = makeup_style.get('makeup_recommendations', [])
                    for rec in recommendations:
                        category = rec.get('category', '未知')
                        style = rec.get('recommended_style', '未知风格')
                        print(f"   - {category}: {style}")
                        
                        if 'tips' in rec:
                            tips = rec['tips'][:2]  # 只显示前两个技巧
                            for tip in tips:
                                print(f"     • {tip}")
                
                return True
            else:
                print(f"❌ 智能推荐失败: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ 智能推荐请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 智能推荐测试失败: {e}")
        return False

def cleanup_demo_files():
    """清理演示文件"""
    demo_files = ["realistic_demo_face.jpg"]
    for file in demo_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🧹 清理演示文件: {file}")

def main():
    """主演示函数"""
    print("🚀 超级高级功能演示开始")
    print("=" * 70)
    
    # 创建演示图片
    demo_image = create_realistic_demo_image()
    if not demo_image:
        print("❌ 无法创建演示图片，退出演示")
        return 1
    
    test_results = []
    
    # 测试各项超级高级功能
    test_results.append(("3D人脸分析", test_3d_analysis(demo_image)))
    test_results.append(("情感AI分析", test_emotion_ai(demo_image)))
    test_results.append(("智能推荐", test_smart_recommendations(demo_image)))
    
    # 清理演示文件
    cleanup_demo_files()
    
    # 输出测试结果
    print("\n" + "=" * 70)
    print("📊 超级高级功能演示结果:")
    print("=" * 70)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("=" * 70)
    print(f"总计: {passed}/{total} 项超级高级功能测试通过")
    
    if passed == total:
        print("🎉 所有超级高级功能测试通过！系统功能极其完善。")
        print("\n🌟 新增超级功能包括:")
        print("   • 3D人脸重建 (头部姿态、深度图、3D线框)")
        print("   • 情感AI分析 (微表情、情感强度、一致性分析)")
        print("   • 智能推荐引擎 (美颜设置、拍照建议、化妆风格)")
        print("   • 高级图像处理 (多维度分析)")
        print("   • 专业级数据分析 (深度统计)")
        print("\n🎯 系统现在具备:")
        print("   ✨ 基础功能: 人脸检测、表情识别、年龄性别预测")
        print("   🚀 高级功能: 美颜滤镜、综合分析、人脸比对")
        print("   ⚡ 超级功能: 3D重建、情感AI、智能推荐")
        print("   📊 数据分析: 使用统计、性能监控、可视化报告")
        return 0
    else:
        print("⚠️  部分超级高级功能测试失败，请检查系统配置。")
        return 1

if __name__ == "__main__":
    exit(main())
