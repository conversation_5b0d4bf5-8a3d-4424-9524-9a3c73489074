import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Clock, 
  Smile,
  Calendar,
  Download,
  RefreshCw
} from 'lucide-react'
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from 'recharts'

const AnalyticsPage = () => {
  const [analyticsData, setAnalyticsData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState(30)

  useEffect(() => {
    fetchAnalyticsData()
  }, [selectedPeriod])

  const fetchAnalyticsData = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/v1/analytics/comprehensive-report?days=${selectedPeriod}`)
      if (response.ok) {
        const data = await response.json()
        setAnalyticsData(data.data)
      }
    } catch (error) {
      console.error('Failed to fetch analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const COLORS = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#84cc16']

  const periods = [
    { value: 7, label: '最近7天' },
    { value: 30, label: '最近30天' },
    { value: 90, label: '最近90天' }
  ]

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto space-y-8">
        <div className="text-center py-12">
          <RefreshCw className="w-8 h-8 mx-auto mb-4 animate-spin text-primary-600" />
          <p className="text-lg text-slate-600">正在加载数据分析...</p>
        </div>
      </div>
    )
  }

  if (!analyticsData) {
    return (
      <div className="max-w-6xl mx-auto space-y-8">
        <div className="text-center py-12">
          <BarChart3 className="w-16 h-16 mx-auto mb-4 text-slate-400" />
          <h3 className="text-lg font-medium text-slate-900 mb-2">暂无数据</h3>
          <p className="text-slate-600">开始使用系统后将显示分析数据</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-slate-900 mb-4">数据分析</h1>
          <p className="text-lg text-slate-600">系统使用情况和分析结果统计</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(Number(e.target.value))}
            className="input"
          >
            {periods.map((period) => (
              <option key={period.value} value={period.value}>
                {period.label}
              </option>
            ))}
          </select>
          
          <button
            onClick={fetchAnalyticsData}
            className="btn-secondary"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新
          </button>
          
          <button
            onClick={() => {
              const dataStr = JSON.stringify(analyticsData, null, 2)
              const dataBlob = new Blob([dataStr], { type: 'application/json' })
              const url = URL.createObjectURL(dataBlob)
              const link = document.createElement('a')
              link.href = url
              link.download = `analytics_report_${Date.now()}.json`
              link.click()
              URL.revokeObjectURL(url)
            }}
            className="btn-primary"
          >
            <Download className="w-4 h-4 mr-2" />
            导出报告
          </button>
        </div>
      </motion.div>

      {/* 总体统计卡片 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-slate-600">总分析次数</p>
              <p className="text-2xl font-bold text-slate-900">
                {analyticsData.summary?.total_analyses || 0}
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <BarChart3 className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-slate-600">检测人脸总数</p>
              <p className="text-2xl font-bold text-slate-900">
                {analyticsData.summary?.total_faces_detected || 0}
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <Users className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-slate-600">成功率</p>
              <p className="text-2xl font-bold text-slate-900">
                {analyticsData.summary?.overall_success_rate || 0}%
              </p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-slate-600">活跃天数</p>
              <p className="text-2xl font-bold text-slate-900">
                {analyticsData.summary?.active_days || 0}
              </p>
            </div>
            <div className="p-3 bg-orange-100 rounded-lg">
              <Calendar className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>
      </motion.div>

      {/* 每日使用趋势 */}
      {analyticsData.daily_stats?.daily_statistics?.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="card p-6"
        >
          <h3 className="text-lg font-semibold text-slate-900 mb-6">每日使用趋势</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={analyticsData.daily_stats.daily_statistics}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="total_analyses" 
                  stroke="#3b82f6" 
                  strokeWidth={2}
                  name="分析次数"
                />
                <Line 
                  type="monotone" 
                  dataKey="total_faces" 
                  stroke="#10b981" 
                  strokeWidth={2}
                  name="检测人脸"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 表情分布 */}
        {analyticsData.emotion_stats?.emotion_statistics?.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="card p-6"
          >
            <h3 className="text-lg font-semibold text-slate-900 mb-6">表情分布</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={analyticsData.emotion_stats.emotion_statistics}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ emotion, percentage }) => `${emotion} ${percentage}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                  >
                    {analyticsData.emotion_stats.emotion_statistics.map((entry: any, index: number) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-4">
              <p className="text-sm text-slate-600">
                最常见表情: <span className="font-medium">{analyticsData.emotion_stats.most_common_emotion}</span>
              </p>
              <p className="text-sm text-slate-600">
                总分析人脸: <span className="font-medium">{analyticsData.emotion_stats.total_faces_analyzed}</span>
              </p>
            </div>
          </motion.div>
        )}

        {/* 年龄分布 */}
        {analyticsData.demographic_stats?.age_statistics?.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="card p-6"
          >
            <h3 className="text-lg font-semibold text-slate-900 mb-6">年龄分布</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={analyticsData.demographic_stats.age_statistics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="age_group" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#3b82f6" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </motion.div>
        )}
      </div>

      {/* 性能统计 */}
      {analyticsData.performance_stats?.performance_statistics?.overall && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="card p-6"
        >
          <h3 className="text-lg font-semibold text-slate-900 mb-6">性能统计</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <Clock className="w-8 h-8 mx-auto mb-2 text-blue-600" />
              <div className="text-2xl font-bold text-blue-600">
                {analyticsData.performance_stats.performance_statistics.overall.avg_processing_time}s
              </div>
              <div className="text-sm text-blue-700">平均处理时间</div>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <TrendingUp className="w-8 h-8 mx-auto mb-2 text-green-600" />
              <div className="text-2xl font-bold text-green-600">
                {analyticsData.performance_stats.performance_statistics.overall.min_processing_time}s
              </div>
              <div className="text-sm text-green-700">最快处理时间</div>
            </div>
            
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <Clock className="w-8 h-8 mx-auto mb-2 text-orange-600" />
              <div className="text-2xl font-bold text-orange-600">
                {analyticsData.performance_stats.performance_statistics.overall.max_processing_time}s
              </div>
              <div className="text-sm text-orange-700">最慢处理时间</div>
            </div>
            
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <BarChart3 className="w-8 h-8 mx-auto mb-2 text-purple-600" />
              <div className="text-2xl font-bold text-purple-600">
                {analyticsData.performance_stats.performance_statistics.overall.total_analyses}
              </div>
              <div className="text-sm text-purple-700">总分析次数</div>
            </div>
          </div>
        </motion.div>
      )}

      {/* 报告信息 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="card p-6 bg-gradient-to-br from-slate-50 to-blue-50"
      >
        <h3 className="text-lg font-semibold text-slate-900 mb-4">报告信息</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-slate-600">
          <div>
            <span className="font-medium">报告周期:</span> {analyticsData.report_period}
          </div>
          <div>
            <span className="font-medium">生成时间:</span> {new Date(analyticsData.generated_at).toLocaleString()}
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default AnalyticsPage
