"""
情感AI分析服务
Emotion AI Analysis Service
"""

import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional
import time
from collections import defaultdict

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from utils.logger import app_logger
from utils.config import settings

class EmotionAI:
    """情感AI分析器"""
    
    def __init__(self):
        """初始化情感AI分析器"""
        self.logger = app_logger
        
        # 情感映射
        self.emotion_mapping = {
            'angry': {'cn': '愤怒', 'intensity': 'high', 'valence': 'negative'},
            'disgust': {'cn': '厌恶', 'intensity': 'medium', 'valence': 'negative'},
            'fear': {'cn': '恐惧', 'intensity': 'high', 'valence': 'negative'},
            'happy': {'cn': '快乐', 'intensity': 'high', 'valence': 'positive'},
            'sad': {'cn': '悲伤', 'intensity': 'medium', 'valence': 'negative'},
            'surprise': {'cn': '惊讶', 'intensity': 'medium', 'valence': 'neutral'},
            'neutral': {'cn': '中性', 'intensity': 'low', 'valence': 'neutral'}
        }
        
        # 情感组合规则
        self.emotion_combinations = {
            ('happy', 'surprise'): {'name': '惊喜', 'description': '快乐与惊讶的结合'},
            ('angry', 'disgust'): {'name': '愤慨', 'description': '愤怒与厌恶的结合'},
            ('sad', 'fear'): {'name': '绝望', 'description': '悲伤与恐惧的结合'},
            ('happy', 'neutral'): {'name': '平静的快乐', 'description': '温和的积极情绪'}
        }
        
        self.logger.info("情感AI分析器初始化完成")
    
    def analyze_micro_expressions(self, landmarks: List[Dict], emotions: List[Dict]) -> Dict:
        """
        分析微表情
        
        Args:
            landmarks: 面部特征点
            emotions: 基础情感分析结果
            
        Returns:
            微表情分析结果
        """
        try:
            if not landmarks or not emotions:
                return {"error": "缺少必要数据"}
            
            points = landmarks[0]['landmarks']
            if len(points) < 68:
                return {"error": "特征点不足"}
            
            micro_expressions = []
            
            # 分析眼部微表情
            eye_analysis = self._analyze_eye_micro_expressions(points)
            if eye_analysis:
                micro_expressions.append(eye_analysis)
            
            # 分析嘴部微表情
            mouth_analysis = self._analyze_mouth_micro_expressions(points)
            if mouth_analysis:
                micro_expressions.append(mouth_analysis)
            
            # 分析眉毛微表情
            eyebrow_analysis = self._analyze_eyebrow_micro_expressions(points)
            if eyebrow_analysis:
                micro_expressions.append(eyebrow_analysis)
            
            # 综合分析
            primary_emotion = emotions[0]['emotion'] if emotions else 'neutral'
            consistency_score = self._calculate_consistency(micro_expressions, primary_emotion)
            
            return {
                "success": True,
                "micro_expressions": micro_expressions,
                "primary_emotion": primary_emotion,
                "consistency_score": consistency_score,
                "interpretation": self._interpret_micro_expressions(micro_expressions, primary_emotion)
            }
            
        except Exception as e:
            self.logger.error(f"微表情分析失败: {str(e)}")
            return {"error": str(e)}
    
    def _analyze_eye_micro_expressions(self, points: List[Dict]) -> Optional[Dict]:
        """分析眼部微表情"""
        try:
            # 左眼分析
            left_eye_top = points[37]['y'] + points[38]['y']
            left_eye_bottom = points[40]['y'] + points[41]['y']
            left_eye_openness = abs(left_eye_bottom - left_eye_top) / 2
            
            # 右眼分析
            right_eye_top = points[43]['y'] + points[44]['y']
            right_eye_bottom = points[46]['y'] + points[47]['y']
            right_eye_openness = abs(right_eye_bottom - right_eye_top) / 2
            
            # 眼部宽度
            left_eye_width = abs(points[39]['x'] - points[36]['x'])
            right_eye_width = abs(points[45]['x'] - points[42]['x'])
            
            # 计算眼部比例
            left_ratio = left_eye_openness / left_eye_width if left_eye_width > 0 else 0
            right_ratio = right_eye_openness / right_eye_width if right_eye_width > 0 else 0
            
            avg_ratio = (left_ratio + right_ratio) / 2
            
            # 判断眼部表情
            if avg_ratio < 0.15:
                expression = "眯眼"
                intensity = "high"
            elif avg_ratio < 0.25:
                expression = "微眯"
                intensity = "medium"
            elif avg_ratio > 0.4:
                expression = "瞪大"
                intensity = "high"
            else:
                expression = "正常"
                intensity = "low"
            
            return {
                "region": "眼部",
                "expression": expression,
                "intensity": intensity,
                "ratio": round(avg_ratio, 3),
                "asymmetry": abs(left_ratio - right_ratio)
            }
            
        except Exception as e:
            self.logger.error(f"眼部微表情分析失败: {str(e)}")
            return None
    
    def _analyze_mouth_micro_expressions(self, points: List[Dict]) -> Optional[Dict]:
        """分析嘴部微表情"""
        try:
            # 嘴角位置
            left_corner = points[48]
            right_corner = points[54]
            
            # 嘴部中心点
            mouth_center_x = (left_corner['x'] + right_corner['x']) / 2
            mouth_center_y = (left_corner['y'] + right_corner['y']) / 2
            
            # 上下唇中心点
            upper_lip = points[51]
            lower_lip = points[57]
            
            # 嘴部宽度和高度
            mouth_width = abs(right_corner['x'] - left_corner['x'])
            mouth_height = abs(lower_lip['y'] - upper_lip['y'])
            
            # 嘴角高度差
            corner_height_diff = abs(left_corner['y'] - right_corner['y'])
            
            # 嘴角相对于中心的高度
            left_corner_relative = left_corner['y'] - mouth_center_y
            right_corner_relative = right_corner['y'] - mouth_center_y
            avg_corner_relative = (left_corner_relative + right_corner_relative) / 2
            
            # 判断嘴部表情
            if avg_corner_relative < -3:
                expression = "上扬"
                intensity = "high" if abs(avg_corner_relative) > 8 else "medium"
            elif avg_corner_relative > 3:
                expression = "下垂"
                intensity = "high" if abs(avg_corner_relative) > 8 else "medium"
            else:
                expression = "平直"
                intensity = "low"
            
            # 嘴部开合程度
            mouth_ratio = mouth_height / mouth_width if mouth_width > 0 else 0
            
            if mouth_ratio > 0.3:
                openness = "张开"
            elif mouth_ratio > 0.15:
                openness = "微张"
            else:
                openness = "闭合"
            
            return {
                "region": "嘴部",
                "expression": expression,
                "intensity": intensity,
                "openness": openness,
                "mouth_ratio": round(mouth_ratio, 3),
                "asymmetry": corner_height_diff
            }
            
        except Exception as e:
            self.logger.error(f"嘴部微表情分析失败: {str(e)}")
            return None
    
    def _analyze_eyebrow_micro_expressions(self, points: List[Dict]) -> Optional[Dict]:
        """分析眉毛微表情"""
        try:
            # 左眉毛点
            left_eyebrow = points[17:22]
            # 右眉毛点
            right_eyebrow = points[22:27]
            
            # 计算眉毛高度
            left_eyebrow_y = sum(p['y'] for p in left_eyebrow) / len(left_eyebrow)
            right_eyebrow_y = sum(p['y'] for p in right_eyebrow) / len(right_eyebrow)
            
            # 眼部中心高度
            left_eye_y = sum(points[i]['y'] for i in range(36, 42)) / 6
            right_eye_y = sum(points[i]['y'] for i in range(42, 48)) / 6
            
            # 眉眼距离
            left_distance = abs(left_eyebrow_y - left_eye_y)
            right_distance = abs(right_eyebrow_y - right_eye_y)
            avg_distance = (left_distance + right_distance) / 2
            
            # 眉毛倾斜度
            left_slope = (left_eyebrow[-1]['y'] - left_eyebrow[0]['y']) / (left_eyebrow[-1]['x'] - left_eyebrow[0]['x'])
            right_slope = (right_eyebrow[-1]['y'] - right_eyebrow[0]['y']) / (right_eyebrow[-1]['x'] - right_eyebrow[0]['x'])
            
            # 判断眉毛表情
            if avg_distance > 25:
                expression = "上扬"
                intensity = "high"
            elif avg_distance > 20:
                expression = "轻微上扬"
                intensity = "medium"
            elif avg_distance < 15:
                expression = "紧皱"
                intensity = "high"
            else:
                expression = "正常"
                intensity = "low"
            
            return {
                "region": "眉毛",
                "expression": expression,
                "intensity": intensity,
                "distance": round(avg_distance, 2),
                "asymmetry": abs(left_distance - right_distance),
                "slope_difference": abs(left_slope - right_slope)
            }
            
        except Exception as e:
            self.logger.error(f"眉毛微表情分析失败: {str(e)}")
            return None
    
    def _calculate_consistency(self, micro_expressions: List[Dict], primary_emotion: str) -> float:
        """计算微表情与主要情感的一致性"""
        try:
            if not micro_expressions:
                return 0.0
            
            # 情感一致性规则
            consistency_rules = {
                'happy': {'眼部': ['正常', '微眯'], '嘴部': ['上扬'], '眉毛': ['正常', '轻微上扬']},
                'sad': {'眼部': ['微眯', '正常'], '嘴部': ['下垂'], '眉毛': ['正常', '紧皱']},
                'angry': {'眼部': ['瞪大', '微眯'], '嘴部': ['下垂', '平直'], '眉毛': ['紧皱']},
                'surprise': {'眼部': ['瞪大'], '嘴部': ['张开', '微张'], '眉毛': ['上扬']},
                'fear': {'眼部': ['瞪大'], '嘴部': ['微张', '平直'], '眉毛': ['上扬', '紧皱']},
                'neutral': {'眼部': ['正常'], '嘴部': ['平直'], '眉毛': ['正常']}
            }
            
            expected = consistency_rules.get(primary_emotion, {})
            if not expected:
                return 0.5  # 未知情感，返回中等一致性
            
            consistent_count = 0
            total_count = 0
            
            for micro_expr in micro_expressions:
                region = micro_expr['region']
                expression = micro_expr['expression']
                
                if region in expected:
                    total_count += 1
                    if expression in expected[region]:
                        consistent_count += 1
            
            return consistent_count / total_count if total_count > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"一致性计算失败: {str(e)}")
            return 0.0
    
    def _interpret_micro_expressions(self, micro_expressions: List[Dict], primary_emotion: str) -> str:
        """解释微表情含义"""
        try:
            interpretations = []
            
            for micro_expr in micro_expressions:
                region = micro_expr['region']
                expression = micro_expr['expression']
                intensity = micro_expr['intensity']
                
                if intensity == 'high':
                    interpretations.append(f"{region}{expression}明显")
                elif intensity == 'medium':
                    interpretations.append(f"{region}{expression}适中")
            
            if not interpretations:
                return f"面部表情与{self.emotion_mapping.get(primary_emotion, {}).get('cn', primary_emotion)}情感基本一致"
            
            return "、".join(interpretations) + f"，整体呈现{self.emotion_mapping.get(primary_emotion, {}).get('cn', primary_emotion)}情感"
            
        except Exception as e:
            self.logger.error(f"微表情解释失败: {str(e)}")
            return "微表情分析结果无法解释"
    
    def analyze_emotion_intensity(self, emotions: List[Dict], landmarks: List[Dict]) -> Dict:
        """
        分析情感强度
        
        Args:
            emotions: 基础情感分析结果
            landmarks: 面部特征点
            
        Returns:
            情感强度分析结果
        """
        try:
            if not emotions:
                return {"error": "无情感数据"}
            
            primary_emotion = emotions[0]
            emotion_name = primary_emotion['emotion']
            base_confidence = primary_emotion['confidence']
            
            # 基于微表情调整强度
            micro_analysis = self.analyze_micro_expressions(landmarks, emotions)
            
            intensity_factors = []
            
            if micro_analysis.get('success'):
                # 一致性影响强度
                consistency = micro_analysis.get('consistency_score', 0.5)
                intensity_factors.append(consistency)
                
                # 微表情强度影响
                for micro_expr in micro_analysis.get('micro_expressions', []):
                    if micro_expr['intensity'] == 'high':
                        intensity_factors.append(0.9)
                    elif micro_expr['intensity'] == 'medium':
                        intensity_factors.append(0.7)
                    else:
                        intensity_factors.append(0.5)
            
            # 计算综合强度
            if intensity_factors:
                avg_intensity = sum(intensity_factors) / len(intensity_factors)
                adjusted_confidence = base_confidence * (0.5 + avg_intensity * 0.5)
            else:
                adjusted_confidence = base_confidence
            
            # 强度等级
            if adjusted_confidence > 0.8:
                intensity_level = "非常强烈"
            elif adjusted_confidence > 0.6:
                intensity_level = "强烈"
            elif adjusted_confidence > 0.4:
                intensity_level = "中等"
            elif adjusted_confidence > 0.2:
                intensity_level = "轻微"
            else:
                intensity_level = "很弱"
            
            return {
                "success": True,
                "emotion": emotion_name,
                "emotion_cn": self.emotion_mapping.get(emotion_name, {}).get('cn', emotion_name),
                "base_confidence": base_confidence,
                "adjusted_confidence": round(adjusted_confidence, 3),
                "intensity_level": intensity_level,
                "valence": self.emotion_mapping.get(emotion_name, {}).get('valence', 'neutral'),
                "micro_expression_support": micro_analysis.get('success', False)
            }
            
        except Exception as e:
            self.logger.error(f"情感强度分析失败: {str(e)}")
            return {"error": str(e)}
    
    def comprehensive_emotion_analysis(self, emotions: List[Dict], landmarks: List[Dict]) -> Dict:
        """
        综合情感分析
        
        Args:
            emotions: 基础情感分析结果
            landmarks: 面部特征点
            
        Returns:
            综合情感分析结果
        """
        start_time = time.time()
        
        try:
            results = {
                "micro_expressions": self.analyze_micro_expressions(landmarks, emotions),
                "emotion_intensity": self.analyze_emotion_intensity(emotions, landmarks),
                "processing_time": 0.0
            }
            
            processing_time = time.time() - start_time
            results["processing_time"] = round(processing_time, 3)
            
            self.logger.info(f"综合情感分析完成，耗时 {processing_time:.3f}s")
            
            return results
            
        except Exception as e:
            self.logger.error(f"综合情感分析失败: {str(e)}")
            return {
                "error": str(e),
                "processing_time": time.time() - start_time
            }
