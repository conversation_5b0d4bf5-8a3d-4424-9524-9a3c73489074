"""
数据分析和统计服务
Analytics and Statistics Service
"""

import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import sqlite3
from collections import defaultdict, Counter
import numpy as np

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from utils.logger import app_logger
from utils.config import settings

class AnalyticsService:
    """数据分析和统计服务"""
    
    def __init__(self):
        """初始化分析服务"""
        self.logger = app_logger
        self.db_path = "analytics.db"
        self._init_database()
        self.logger.info("数据分析服务初始化完成")
    
    def _init_database(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建分析记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS analysis_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    analysis_type TEXT NOT NULL,
                    faces_detected INTEGER DEFAULT 0,
                    emotions TEXT,
                    age_groups TEXT,
                    genders TEXT,
                    processing_time REAL,
                    image_size TEXT,
                    success BOOLEAN DEFAULT TRUE
                )
            ''')
            
            # 创建用户会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT UNIQUE,
                    start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
                    total_analyses INTEGER DEFAULT 0,
                    user_agent TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {str(e)}")
    
    def record_analysis(self, analysis_data: Dict) -> bool:
        """
        记录分析数据
        
        Args:
            analysis_data: 分析数据
            
        Returns:
            是否记录成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 提取数据
            analysis_type = analysis_data.get('type', 'unknown')
            faces_detected = len(analysis_data.get('faces', []))
            emotions = json.dumps([e.get('emotion', 'unknown') for e in analysis_data.get('emotions', [])])
            age_groups = json.dumps([ag.get('age', {}).get('age_range', 'unknown') for ag in analysis_data.get('age_gender', [])])
            genders = json.dumps([ag.get('gender', {}).get('gender', 'unknown') for ag in analysis_data.get('age_gender', [])])
            processing_time = analysis_data.get('processing_time', 0.0)
            image_size = f"{analysis_data.get('image_width', 0)}x{analysis_data.get('image_height', 0)}"
            success = analysis_data.get('success', True)
            
            cursor.execute('''
                INSERT INTO analysis_records 
                (analysis_type, faces_detected, emotions, age_groups, genders, processing_time, image_size, success)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (analysis_type, faces_detected, emotions, age_groups, genders, processing_time, image_size, success))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            self.logger.error(f"记录分析数据失败: {str(e)}")
            return False
    
    def get_daily_statistics(self, days: int = 7) -> Dict:
        """
        获取每日统计数据
        
        Args:
            days: 统计天数
            
        Returns:
            每日统计数据
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取指定天数内的数据
            start_date = datetime.now() - timedelta(days=days)
            
            cursor.execute('''
                SELECT DATE(timestamp) as date, 
                       COUNT(*) as total_analyses,
                       SUM(faces_detected) as total_faces,
                       AVG(processing_time) as avg_processing_time,
                       SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_analyses
                FROM analysis_records 
                WHERE timestamp >= ?
                GROUP BY DATE(timestamp)
                ORDER BY date
            ''', (start_date,))
            
            results = cursor.fetchall()
            conn.close()
            
            daily_stats = []
            for row in results:
                daily_stats.append({
                    "date": row[0],
                    "total_analyses": row[1],
                    "total_faces": row[2],
                    "avg_processing_time": round(row[3], 3) if row[3] else 0,
                    "successful_analyses": row[4],
                    "success_rate": round(row[4] / row[1] * 100, 2) if row[1] > 0 else 0
                })
            
            return {
                "daily_statistics": daily_stats,
                "period_days": days,
                "total_days": len(daily_stats)
            }
            
        except Exception as e:
            self.logger.error(f"获取每日统计失败: {str(e)}")
            return {"daily_statistics": [], "error": str(e)}
    
    def get_emotion_statistics(self, days: int = 30) -> Dict:
        """
        获取表情统计数据
        
        Args:
            days: 统计天数
            
        Returns:
            表情统计数据
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            start_date = datetime.now() - timedelta(days=days)
            
            cursor.execute('''
                SELECT emotions FROM analysis_records 
                WHERE timestamp >= ? AND emotions IS NOT NULL
            ''', (start_date,))
            
            results = cursor.fetchall()
            conn.close()
            
            # 统计表情分布
            emotion_counter = Counter()
            total_faces = 0
            
            for row in results:
                try:
                    emotions = json.loads(row[0])
                    for emotion in emotions:
                        if emotion != 'unknown':
                            emotion_counter[emotion] += 1
                            total_faces += 1
                except:
                    continue
            
            # 计算百分比
            emotion_stats = []
            for emotion, count in emotion_counter.most_common():
                percentage = round(count / total_faces * 100, 2) if total_faces > 0 else 0
                emotion_stats.append({
                    "emotion": emotion,
                    "count": count,
                    "percentage": percentage
                })
            
            return {
                "emotion_statistics": emotion_stats,
                "total_faces_analyzed": total_faces,
                "period_days": days,
                "most_common_emotion": emotion_stats[0]["emotion"] if emotion_stats else "无数据"
            }
            
        except Exception as e:
            self.logger.error(f"获取表情统计失败: {str(e)}")
            return {"emotion_statistics": [], "error": str(e)}
    
    def get_demographic_statistics(self, days: int = 30) -> Dict:
        """
        获取人口统计数据
        
        Args:
            days: 统计天数
            
        Returns:
            人口统计数据
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            start_date = datetime.now() - timedelta(days=days)
            
            cursor.execute('''
                SELECT age_groups, genders FROM analysis_records 
                WHERE timestamp >= ? AND age_groups IS NOT NULL AND genders IS NOT NULL
            ''', (start_date,))
            
            results = cursor.fetchall()
            conn.close()
            
            # 统计年龄和性别分布
            age_counter = Counter()
            gender_counter = Counter()
            total_faces = 0
            
            for row in results:
                try:
                    age_groups = json.loads(row[0])
                    genders = json.loads(row[1])
                    
                    for age in age_groups:
                        if age != 'unknown':
                            age_counter[age] += 1
                            total_faces += 1
                    
                    for gender in genders:
                        if gender != 'unknown':
                            gender_counter[gender] += 1
                except:
                    continue
            
            # 年龄统计
            age_stats = []
            for age, count in age_counter.most_common():
                percentage = round(count / total_faces * 100, 2) if total_faces > 0 else 0
                age_stats.append({
                    "age_group": age,
                    "count": count,
                    "percentage": percentage
                })
            
            # 性别统计
            gender_stats = []
            for gender, count in gender_counter.most_common():
                percentage = round(count / total_faces * 100, 2) if total_faces > 0 else 0
                gender_stats.append({
                    "gender": gender,
                    "count": count,
                    "percentage": percentage
                })
            
            return {
                "age_statistics": age_stats,
                "gender_statistics": gender_stats,
                "total_faces_analyzed": total_faces,
                "period_days": days
            }
            
        except Exception as e:
            self.logger.error(f"获取人口统计失败: {str(e)}")
            return {"age_statistics": [], "gender_statistics": [], "error": str(e)}
    
    def get_performance_statistics(self, days: int = 7) -> Dict:
        """
        获取性能统计数据
        
        Args:
            days: 统计天数
            
        Returns:
            性能统计数据
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            start_date = datetime.now() - timedelta(days=days)
            
            cursor.execute('''
                SELECT processing_time, analysis_type, faces_detected 
                FROM analysis_records 
                WHERE timestamp >= ? AND processing_time IS NOT NULL
            ''', (start_date,))
            
            results = cursor.fetchall()
            conn.close()
            
            if not results:
                return {"performance_statistics": {}, "message": "无性能数据"}
            
            processing_times = [row[0] for row in results]
            
            # 按分析类型统计
            type_stats = defaultdict(list)
            for row in results:
                type_stats[row[1]].append(row[0])
            
            # 按人脸数量统计
            face_count_stats = defaultdict(list)
            for row in results:
                face_count_stats[row[2]].append(row[0])
            
            performance_stats = {
                "overall": {
                    "avg_processing_time": round(np.mean(processing_times), 3),
                    "min_processing_time": round(np.min(processing_times), 3),
                    "max_processing_time": round(np.max(processing_times), 3),
                    "median_processing_time": round(np.median(processing_times), 3),
                    "total_analyses": len(processing_times)
                },
                "by_analysis_type": {},
                "by_face_count": {}
            }
            
            # 按类型统计
            for analysis_type, times in type_stats.items():
                performance_stats["by_analysis_type"][analysis_type] = {
                    "avg_processing_time": round(np.mean(times), 3),
                    "count": len(times)
                }
            
            # 按人脸数量统计
            for face_count, times in face_count_stats.items():
                performance_stats["by_face_count"][str(face_count)] = {
                    "avg_processing_time": round(np.mean(times), 3),
                    "count": len(times)
                }
            
            return {
                "performance_statistics": performance_stats,
                "period_days": days
            }
            
        except Exception as e:
            self.logger.error(f"获取性能统计失败: {str(e)}")
            return {"performance_statistics": {}, "error": str(e)}
    
    def get_comprehensive_report(self, days: int = 30) -> Dict:
        """
        获取综合报告
        
        Args:
            days: 统计天数
            
        Returns:
            综合报告
        """
        try:
            report = {
                "report_period": f"最近 {days} 天",
                "generated_at": datetime.now().isoformat(),
                "daily_stats": self.get_daily_statistics(days),
                "emotion_stats": self.get_emotion_statistics(days),
                "demographic_stats": self.get_demographic_statistics(days),
                "performance_stats": self.get_performance_statistics(days)
            }
            
            # 计算总体摘要
            daily_data = report["daily_stats"]["daily_statistics"]
            if daily_data:
                total_analyses = sum(day["total_analyses"] for day in daily_data)
                total_faces = sum(day["total_faces"] for day in daily_data)
                avg_success_rate = np.mean([day["success_rate"] for day in daily_data])
                
                report["summary"] = {
                    "total_analyses": total_analyses,
                    "total_faces_detected": total_faces,
                    "avg_faces_per_analysis": round(total_faces / total_analyses, 2) if total_analyses > 0 else 0,
                    "overall_success_rate": round(avg_success_rate, 2),
                    "active_days": len(daily_data)
                }
            else:
                report["summary"] = {
                    "total_analyses": 0,
                    "total_faces_detected": 0,
                    "avg_faces_per_analysis": 0,
                    "overall_success_rate": 0,
                    "active_days": 0
                }
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成综合报告失败: {str(e)}")
            return {"error": str(e)}
