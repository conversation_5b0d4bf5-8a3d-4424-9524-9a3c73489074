#!/usr/bin/env python3
"""
人脸分析系统测试脚本
用于验证系统各个组件是否正常工作
"""

import requests
import json
import time
import os
from pathlib import Path

def test_backend_health():
    """测试后端健康状态"""
    print("🔍 测试后端健康状态...")
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 后端服务正常: {data['message']}")
            return True
        else:
            print(f"❌ 后端服务异常: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到后端服务: {e}")
        return False

def test_system_info():
    """测试系统信息接口"""
    print("\n📋 获取系统信息...")
    try:
        response = requests.get("http://localhost:8000/info", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 系统: {data['system']}")
            print(f"✅ 版本: {data['version']}")
            print(f"✅ 功能: {', '.join(data['features'])}")
            return True
        else:
            print(f"❌ 获取系统信息失败: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_database_info():
    """测试人脸数据库信息"""
    print("\n💾 测试人脸数据库...")
    try:
        response = requests.get("http://localhost:8000/api/v1/database-info", timeout=5)
        if response.status_code == 200:
            data = response.json()
            db_info = data.get('database_info', {})
            print(f"✅ 数据库人脸数: {db_info.get('total_faces', 0)}")
            print(f"✅ 识别模型: {db_info.get('model', 'unknown')}")
            print(f"✅ 匹配阈值: {db_info.get('tolerance', 'unknown')}")
            return True
        else:
            print(f"❌ 获取数据库信息失败: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_batch_status():
    """测试批量处理状态"""
    print("\n📦 测试批量处理功能...")
    try:
        response = requests.get("http://localhost:8000/api/v1/batch/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 批量处理状态: {data['status']}")
            print(f"✅ 最大文件数: {data['max_files']}")
            print(f"✅ 支持格式: {', '.join(data['supported_formats'])}")
            return True
        else:
            print(f"❌ 获取批量处理状态失败: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_realtime_status():
    """测试实时分析状态"""
    print("\n📹 测试实时分析功能...")
    try:
        response = requests.get("http://localhost:8000/api/v1/realtime/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 实时分析状态: {data['status']}")
            print(f"✅ 活跃连接数: {data['active_connections']}")
            print(f"✅ 支持功能: {', '.join(data['features'])}")
            return True
        else:
            print(f"❌ 获取实时分析状态失败: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_frontend_access():
    """测试前端访问"""
    print("\n🌐 测试前端服务...")
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务正常运行")
            return True
        else:
            print(f"❌ 前端服务异常: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法访问前端服务: {e}")
        return False

def create_test_image():
    """创建测试图片"""
    try:
        import cv2
        import numpy as np
        
        # 创建一个简单的测试图片
        img = np.zeros((300, 300, 3), dtype=np.uint8)
        img.fill(255)  # 白色背景
        
        # 添加一些简单的图形作为"人脸"
        cv2.circle(img, (150, 150), 80, (200, 200, 200), -1)  # 脸部
        cv2.circle(img, (120, 120), 10, (0, 0, 0), -1)       # 左眼
        cv2.circle(img, (180, 120), 10, (0, 0, 0), -1)       # 右眼
        cv2.ellipse(img, (150, 180), (30, 15), 0, 0, 180, (0, 0, 0), 2)  # 嘴巴
        
        test_image_path = "test_face.jpg"
        cv2.imwrite(test_image_path, img)
        print(f"✅ 创建测试图片: {test_image_path}")
        return test_image_path
    except ImportError:
        print("⚠️  OpenCV未安装，跳过图片测试")
        return None
    except Exception as e:
        print(f"❌ 创建测试图片失败: {e}")
        return None

def test_image_analysis(image_path):
    """测试图片分析功能"""
    if not image_path or not os.path.exists(image_path):
        print("⚠️  跳过图片分析测试（无测试图片）")
        return False
        
    print(f"\n🖼️  测试图片分析功能...")
    try:
        with open(image_path, 'rb') as f:
            files = {'file': ('test_face.jpg', f, 'image/jpeg')}
            response = requests.post(
                "http://localhost:8000/api/v1/analyze/complete",
                files=files,
                timeout=30
            )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                results = data.get('results', {})
                stats = results.get('statistics', {})
                print(f"✅ 分析成功!")
                print(f"✅ 检测到人脸数: {stats.get('total_faces', 0)}")
                print(f"✅ 处理时间: {data.get('processing_time', 0):.2f}秒")
                return True
            else:
                print(f"❌ 分析失败: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ 图片分析请求失败: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    test_files = ["test_face.jpg"]
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🧹 清理测试文件: {file}")

def main():
    """主测试函数"""
    print("🚀 人脸分析系统测试开始")
    print("=" * 50)
    
    test_results = []
    
    # 测试后端服务
    test_results.append(("后端健康检查", test_backend_health()))
    test_results.append(("系统信息", test_system_info()))
    test_results.append(("数据库信息", test_database_info()))
    test_results.append(("批量处理状态", test_batch_status()))
    test_results.append(("实时分析状态", test_realtime_status()))
    
    # 测试前端服务
    test_results.append(("前端访问", test_frontend_access()))
    
    # 测试图片分析功能
    test_image_path = create_test_image()
    test_results.append(("图片分析", test_image_analysis(test_image_path)))
    
    # 清理测试文件
    cleanup_test_files()
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查系统配置。")
        return 1

if __name__ == "__main__":
    exit(main())
