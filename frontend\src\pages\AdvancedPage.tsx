import React, { useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { useDropzone } from 'react-dropzone'
import {
  Upload,
  <PERSON><PERSON><PERSON>,
  BarChart3,
  <PERSON>ap,
  Eye,
  <PERSON>lette,
  Loader2,
  Download,
  <PERSON><PERSON><PERSON>om<PERSON><PERSON>,
  <PERSON>
} from 'lucide-react'
import toast from 'react-hot-toast'

const AdvancedPage = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [analysisResult, setAnalysisResult] = useState<any>(null)
  const [activeTab, setActiveTab] = useState('comprehensive')

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (file) {
      setSelectedFile(file)
      const url = URL.createObjectURL(file)
      setPreviewUrl(url)
      setAnalysisResult(null)
      toast.success('图片上传成功！')
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.bmp', '.tiff']
    },
    multiple: false,
    maxSize: 10 * 1024 * 1024
  })

  const handleComprehensiveAnalysis = async () => {
    if (!selectedFile) {
      toast.error('请先选择一张图片')
      return
    }

    setIsProcessing(true)
    
    try {
      const formData = new FormData()
      formData.append('file', selectedFile)

      const response = await fetch('/api/v1/advanced/comprehensive-analysis', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error('综合分析失败')
      }

      const result = await response.json()
      setAnalysisResult(result)
      toast.success('综合分析完成！')
      
    } catch (error) {
      console.error('Analysis error:', error)
      toast.error('分析失败，请重试')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleBeautyFilter = async () => {
    if (!selectedFile) {
      toast.error('请先选择一张图片')
      return
    }

    setIsProcessing(true)
    
    try {
      const formData = new FormData()
      formData.append('file', selectedFile)
      
      const beautySettings = {
        skin_smoothing: 0.6,
        brightness: 15,
        contrast: 1.2,
        saturation: 1.3,
        eye_enhancement: 0.4,
        teeth_whitening: 0.3,
        blemish_removal: 0.5
      }
      
      formData.append('settings', JSON.stringify(beautySettings))

      const response = await fetch('/api/v1/advanced/beauty-filter', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error('美颜处理失败')
      }

      const result = await response.json()
      setAnalysisResult(result)
      toast.success('美颜处理完成！')
      
    } catch (error) {
      console.error('Beauty filter error:', error)
      toast.error('美颜处理失败，请重试')
    } finally {
      setIsProcessing(false)
    }
  }

  const tabs = [
    { id: 'comprehensive', label: '综合分析', icon: Brain, action: handleComprehensiveAnalysis },
    { id: 'beauty', label: '美颜滤镜', icon: Sparkles, action: handleBeautyFilter },
    { id: 'comparison', label: '人脸比对', icon: GitCompare, action: () => toast.info('功能开发中') },
    { id: 'analytics', label: '数据分析', icon: BarChart3, action: () => toast.info('功能开发中') }
  ]

  const renderComprehensiveResults = () => {
    if (!analysisResult?.results?.advanced_analysis) return null

    const analysis = analysisResult.results.advanced_analysis[0]
    
    return (
      <div className="space-y-6">
        {/* 面部对称性 */}
        {analysis.symmetry && (
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">面部对称性分析</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {(analysis.symmetry.symmetry_score * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-blue-700">对称度评分</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-lg font-semibold text-green-600">
                  {analysis.symmetry.analysis}
                </div>
                <div className="text-sm text-green-700">分析结果</div>
              </div>
            </div>
          </div>
        )}

        {/* 面部形状 */}
        {analysis.face_shape && (
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">面部形状分析</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-xl font-bold text-purple-600">
                  {analysis.face_shape.face_shape}
                </div>
                <div className="text-sm text-purple-700">面部形状</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-xl font-bold text-orange-600">
                  {(analysis.face_shape.confidence * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-orange-700">置信度</div>
              </div>
            </div>
          </div>
        )}

        {/* 肤色分析 */}
        {analysis.skin_tone && (
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">肤色分析</h3>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-lg font-semibold text-yellow-600">
                  {analysis.skin_tone.skin_tone}
                </div>
                <div className="text-sm text-yellow-700">肤色类型</div>
              </div>
              <div className="text-center p-4 bg-pink-50 rounded-lg">
                <div 
                  className="w-8 h-8 rounded-full mx-auto mb-2 border-2 border-white shadow-md"
                  style={{ backgroundColor: analysis.skin_tone.hex_color }}
                ></div>
                <div className="text-sm text-pink-700">{analysis.skin_tone.hex_color}</div>
              </div>
              <div className="text-center p-4 bg-indigo-50 rounded-lg">
                <div className="text-lg font-semibold text-indigo-600">
                  {analysis.skin_tone.brightness?.toFixed(1)}
                </div>
                <div className="text-sm text-indigo-700">亮度值</div>
              </div>
            </div>
          </div>
        )}

        {/* 面部特征 */}
        {analysis.facial_features?.features && (
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">面部特征分析</h3>
            <div className="space-y-4">
              {Object.entries(analysis.facial_features.features).map(([feature, data]: [string, any]) => (
                <div key={feature} className="p-4 bg-slate-50 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="font-medium text-slate-900 capitalize">
                      {feature === 'eyes' ? '眼部' : feature === 'nose' ? '鼻部' : '嘴部'}
                    </span>
                    <span className="text-slate-600">{data.analysis}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }

  const renderBeautyResults = () => {
    if (!analysisResult?.images) return null

    return (
      <div className="space-y-6">
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-slate-900 mb-4">美颜效果对比</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-sm font-medium text-slate-700 mb-2">原图</h4>
              <img
                src={analysisResult.images.original}
                alt="Original"
                className="w-full rounded-lg shadow-md"
              />
            </div>
            <div>
              <h4 className="text-sm font-medium text-slate-700 mb-2">美颜后</h4>
              <img
                src={analysisResult.images.enhanced}
                alt="Enhanced"
                className="w-full rounded-lg shadow-md"
              />
            </div>
          </div>
          
          {analysisResult.settings_applied && (
            <div className="mt-6 p-4 bg-slate-50 rounded-lg">
              <h4 className="text-sm font-medium text-slate-700 mb-2">应用的美颜设置</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                {Object.entries(analysisResult.settings_applied).map(([key, value]: [string, any]) => (
                  <div key={key} className="text-slate-600">
                    <span className="font-medium">{key}:</span> {value}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h1 className="text-3xl font-bold text-slate-900 mb-4">
          高级功能
        </h1>
        <p className="text-lg text-slate-600">
          体验更多强大的AI面部分析和图像处理功能
        </p>
      </motion.div>

      {/* 功能选项卡 */}
      <div className="card p-6">
        <div className="flex flex-wrap gap-2 mb-6">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-primary-100 text-primary-700'
                    : 'text-slate-600 hover:bg-slate-100'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </div>

        {/* 图片上传区域 */}
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200 mb-6 ${
            isDragActive
              ? 'border-primary-500 bg-primary-50'
              : 'border-slate-300 hover:border-primary-400 hover:bg-slate-50'
          }`}
        >
          <input {...getInputProps()} />
          <div className="space-y-4">
            <div className="mx-auto w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center">
              <Upload className="w-8 h-8 text-slate-400" />
            </div>
            <div>
              <p className="text-lg font-medium text-slate-900">
                {isDragActive ? '释放文件到这里' : '上传图片进行高级分析'}
              </p>
              <p className="text-sm text-slate-500 mt-1">
                支持 JPG, PNG, BMP, TIFF 格式
              </p>
            </div>
          </div>
        </div>

        {/* 图片预览和操作按钮 */}
        {previewUrl && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-slate-900 mb-4">图片预览</h3>
              <img
                src={previewUrl}
                alt="Preview"
                className="w-full h-64 object-cover rounded-lg"
              />
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-slate-900 mb-4">操作选项</h3>
              <div className="space-y-3">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={tab.action}
                      disabled={isProcessing}
                      className="btn-primary w-full py-3 flex items-center justify-center"
                    >
                      {isProcessing ? (
                        <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                      ) : (
                        <Icon className="w-5 h-5 mr-2" />
                      )}
                      {tab.label}
                    </button>
                  )
                })}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 分析结果 */}
      {analysisResult && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* 处理统计 */}
          <div className="card p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-slate-900">处理结果</h3>
              {analysisResult.images?.enhanced && (
                <button
                  onClick={() => {
                    const link = document.createElement('a')
                    link.href = analysisResult.images.enhanced
                    link.download = `enhanced_${Date.now()}.jpg`
                    link.click()
                  }}
                  className="btn-secondary text-sm"
                >
                  <Download className="w-4 h-4 mr-1" />
                  下载结果
                </button>
              )}
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {analysisResult.faces_detected || analysisResult.results?.basic_detection?.faces?.length || 0}
                </div>
                <div className="text-sm text-blue-700">检测到的人脸</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {analysisResult.processing_time?.toFixed(2) || 0}s
                </div>
                <div className="text-sm text-green-700">处理时间</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {analysisResult.success ? '成功' : '失败'}
                </div>
                <div className="text-sm text-purple-700">处理状态</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">
                  {activeTab === 'comprehensive' ? '综合' : activeTab === 'beauty' ? '美颜' : '其他'}
                </div>
                <div className="text-sm text-orange-700">分析类型</div>
              </div>
            </div>
          </div>

          {/* 具体结果 */}
          {activeTab === 'comprehensive' && renderComprehensiveResults()}
          {activeTab === 'beauty' && renderBeautyResults()}
        </motion.div>
      )}
    </div>
  )
}

export default AdvancedPage
