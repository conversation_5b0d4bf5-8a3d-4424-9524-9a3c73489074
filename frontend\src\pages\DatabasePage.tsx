import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Database, Plus, Trash2, Search, User, Upload } from 'lucide-react'
import toast from 'react-hot-toast'

const DatabasePage = () => {
  const [databaseInfo, setDatabaseInfo] = useState<any>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [isAddingFace, setIsAddingFace] = useState(false)
  const [newFaceName, setNewFaceName] = useState('')
  const [newFaceFile, setNewFaceFile] = useState<File | null>(null)

  useEffect(() => {
    fetchDatabaseInfo()
  }, [])

  const fetchDatabaseInfo = async () => {
    try {
      const response = await fetch('/api/v1/database-info')
      if (response.ok) {
        const data = await response.json()
        setDatabaseInfo(data.database_info)
      }
    } catch (error) {
      console.error('Failed to fetch database info:', error)
    }
  }

  const handleAddFace = async () => {
    if (!newFaceName.trim() || !newFaceFile) {
      toast.error('请输入姓名并选择图片')
      return
    }

    const formData = new FormData()
    formData.append('file', newFaceFile)
    formData.append('name', newFaceName.trim())

    try {
      const response = await fetch('/api/v1/add-face', {
        method: 'POST',
        body: formData
      })

      if (response.ok) {
        toast.success('人脸添加成功！')
        setNewFaceName('')
        setNewFaceFile(null)
        setIsAddingFace(false)
        fetchDatabaseInfo()
      } else {
        throw new Error('添加失败')
      }
    } catch (error) {
      toast.error('添加人脸失败，请重试')
    }
  }

  const filteredNames = databaseInfo?.names?.filter((name: string) =>
    name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h1 className="text-3xl font-bold text-slate-900 mb-4">
          人脸数据库
        </h1>
        <p className="text-lg text-slate-600">
          管理已知人脸数据，用于人脸识别和验证
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 左侧：数据库统计和操作 */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-6"
        >
          {/* 数据库统计 */}
          <div className="card p-6">
            <h2 className="text-xl font-semibold text-slate-900 mb-4">
              数据库统计
            </h2>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-slate-600">总人脸数</span>
                <span className="text-2xl font-bold text-primary-600">
                  {databaseInfo?.total_faces || 0}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-slate-600">识别模型</span>
                <span className="badge badge-primary">
                  {databaseInfo?.model || 'large'}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-slate-600">匹配阈值</span>
                <span className="font-medium">
                  {databaseInfo?.tolerance || 0.6}
                </span>
              </div>
            </div>
          </div>

          {/* 添加新人脸 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">
              添加新人脸
            </h3>
            
            {!isAddingFace ? (
              <button
                onClick={() => setIsAddingFace(true)}
                className="btn-primary w-full py-3"
              >
                <Plus className="w-5 h-5 mr-2" />
                添加人脸
              </button>
            ) : (
              <div className="space-y-4">
                <div>
                  <label className="label">姓名</label>
                  <input
                    type="text"
                    value={newFaceName}
                    onChange={(e) => setNewFaceName(e.target.value)}
                    placeholder="输入姓名"
                    className="input"
                  />
                </div>
                
                <div>
                  <label className="label">选择图片</label>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => setNewFaceFile(e.target.files?.[0] || null)}
                    className="input"
                  />
                </div>
                
                <div className="flex space-x-2">
                  <button
                    onClick={handleAddFace}
                    className="btn-success flex-1"
                  >
                    <Upload className="w-4 h-4 mr-1" />
                    确认添加
                  </button>
                  <button
                    onClick={() => {
                      setIsAddingFace(false)
                      setNewFaceName('')
                      setNewFaceFile(null)
                    }}
                    className="btn-secondary flex-1"
                  >
                    取消
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* 数据库操作 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">
              数据库操作
            </h3>
            
            <div className="space-y-3">
              <button className="btn-secondary w-full">
                <Database className="w-4 h-4 mr-2" />
                导出数据库
              </button>
              
              <button className="btn-secondary w-full">
                <Upload className="w-4 h-4 mr-2" />
                导入数据库
              </button>
              
              <button className="btn-error w-full">
                <Trash2 className="w-4 h-4 mr-2" />
                清空数据库
              </button>
            </div>
          </div>
        </motion.div>

        {/* 右侧：人脸列表 */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="lg:col-span-2 space-y-6"
        >
          {/* 搜索栏 */}
          <div className="card p-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="搜索人脸..."
                className="input pl-10"
              />
            </div>
          </div>

          {/* 人脸列表 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">
              已知人脸列表
            </h3>
            
            {filteredNames.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {filteredNames.map((name: string, index: number) => (
                  <div key={index} className="p-4 bg-slate-50 rounded-lg flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-primary-600" />
                      </div>
                      <div>
                        <div className="font-medium text-slate-900">{name}</div>
                        <div className="text-sm text-slate-500">ID: {index + 1}</div>
                      </div>
                    </div>
                    
                    <button
                      onClick={() => {
                        // 这里可以添加删除功能
                        toast.success(`删除 ${name} 的人脸数据`)
                      }}
                      className="text-red-500 hover:text-red-700 p-1"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Database className="w-8 h-8 text-slate-400" />
                </div>
                <h4 className="text-lg font-medium text-slate-900 mb-2">
                  {searchTerm ? '未找到匹配的人脸' : '数据库为空'}
                </h4>
                <p className="text-slate-600">
                  {searchTerm ? '尝试使用其他关键词搜索' : '添加第一个人脸开始使用'}
                </p>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default DatabasePage
