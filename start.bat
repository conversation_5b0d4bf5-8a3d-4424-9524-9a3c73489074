@echo off
echo 启动人脸面部分析系统...
echo.

echo 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo 检查Node.js环境...
node --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js，请先安装Node.js 16+
    pause
    exit /b 1
)

echo.
echo 安装后端依赖...
cd backend
if not exist venv (
    echo 创建虚拟环境...
    python -m venv venv
)

echo 激活虚拟环境...
call venv\Scripts\activate

echo 安装Python依赖...
pip install -r requirements.txt

echo.
echo 启动后端服务...
start "后端服务" cmd /k "venv\Scripts\activate && python main.py"

cd ..\frontend

echo.
echo 安装前端依赖...
if not exist node_modules (
    npm install
)

echo.
echo 启动前端服务...
start "前端服务" cmd /k "npm run dev"

echo.
echo 系统启动完成！
echo 后端服务: http://localhost:8000
echo 前端服务: http://localhost:3000
echo API文档: http://localhost:8000/docs
echo.
echo 按任意键退出...
pause
