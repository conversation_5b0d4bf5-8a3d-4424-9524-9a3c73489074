"""
图像增强和美颜服务
Image Enhancement and Beauty Filter Service
"""

import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional
import time

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from utils.logger import app_logger
from utils.config import settings

class ImageEnhancement:
    """图像增强和美颜处理器"""
    
    def __init__(self):
        """初始化图像增强器"""
        self.logger = app_logger
        self.logger.info("图像增强器初始化完成")
    
    def skin_smoothing(self, image: np.ndarray, intensity: float = 0.5) -> np.ndarray:
        """
        皮肤平滑处理
        
        Args:
            image: 输入图像
            intensity: 平滑强度 (0.0-1.0)
            
        Returns:
            处理后的图像
        """
        try:
            if intensity <= 0:
                return image
            
            # 双边滤波进行皮肤平滑
            d = int(15 * intensity)
            sigma_color = int(80 * intensity)
            sigma_space = int(80 * intensity)
            
            smoothed = cv2.bilateralFilter(image, d, sigma_color, sigma_space)
            
            # 混合原图和平滑图
            result = cv2.addWeighted(image, 1 - intensity, smoothed, intensity, 0)
            
            return result
            
        except Exception as e:
            self.logger.error(f"皮肤平滑处理失败: {str(e)}")
            return image
    
    def brightness_adjustment(self, image: np.ndarray, brightness: float = 0.0) -> np.ndarray:
        """
        亮度调整
        
        Args:
            image: 输入图像
            brightness: 亮度调整值 (-100 到 100)
            
        Returns:
            调整后的图像
        """
        try:
            if brightness == 0:
                return image
            
            # 转换为浮点数进行计算
            img_float = image.astype(np.float32)
            
            # 调整亮度
            img_float += brightness
            
            # 限制像素值范围
            img_float = np.clip(img_float, 0, 255)
            
            return img_float.astype(np.uint8)
            
        except Exception as e:
            self.logger.error(f"亮度调整失败: {str(e)}")
            return image
    
    def contrast_adjustment(self, image: np.ndarray, contrast: float = 1.0) -> np.ndarray:
        """
        对比度调整
        
        Args:
            image: 输入图像
            contrast: 对比度系数 (0.5-2.0)
            
        Returns:
            调整后的图像
        """
        try:
            if contrast == 1.0:
                return image
            
            # 转换为浮点数
            img_float = image.astype(np.float32)
            
            # 调整对比度
            img_float = img_float * contrast
            
            # 限制像素值范围
            img_float = np.clip(img_float, 0, 255)
            
            return img_float.astype(np.uint8)
            
        except Exception as e:
            self.logger.error(f"对比度调整失败: {str(e)}")
            return image
    
    def saturation_adjustment(self, image: np.ndarray, saturation: float = 1.0) -> np.ndarray:
        """
        饱和度调整
        
        Args:
            image: 输入图像
            saturation: 饱和度系数 (0.0-2.0)
            
        Returns:
            调整后的图像
        """
        try:
            if saturation == 1.0:
                return image
            
            # 转换到HSV色彩空间
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV).astype(np.float32)
            
            # 调整饱和度
            hsv[:, :, 1] = hsv[:, :, 1] * saturation
            
            # 限制饱和度范围
            hsv[:, :, 1] = np.clip(hsv[:, :, 1], 0, 255)
            
            # 转换回BGR
            result = cv2.cvtColor(hsv.astype(np.uint8), cv2.COLOR_HSV2BGR)
            
            return result
            
        except Exception as e:
            self.logger.error(f"饱和度调整失败: {str(e)}")
            return image
    
    def eye_enhancement(self, image: np.ndarray, landmarks: List[Dict], intensity: float = 0.3) -> np.ndarray:
        """
        眼部增强
        
        Args:
            image: 输入图像
            landmarks: 面部特征点
            intensity: 增强强度 (0.0-1.0)
            
        Returns:
            增强后的图像
        """
        try:
            if not landmarks or intensity <= 0:
                return image
            
            result = image.copy()
            points = landmarks[0]['landmarks']
            
            if len(points) < 68:
                return image
            
            # 左眼区域 (points 36-41)
            left_eye_points = np.array([[points[i]['x'], points[i]['y']] for i in range(36, 42)], np.int32)
            
            # 右眼区域 (points 42-47)
            right_eye_points = np.array([[points[i]['x'], points[i]['y']] for i in range(42, 48)], np.int32)
            
            # 创建眼部遮罩
            mask = np.zeros(image.shape[:2], dtype=np.uint8)
            cv2.fillPoly(mask, [left_eye_points], 255)
            cv2.fillPoly(mask, [right_eye_points], 255)
            
            # 扩展遮罩区域
            kernel = np.ones((10, 10), np.uint8)
            mask = cv2.dilate(mask, kernel, iterations=1)
            
            # 对眼部区域进行锐化
            kernel_sharpen = np.array([[-1, -1, -1],
                                     [-1,  9, -1],
                                     [-1, -1, -1]])
            
            sharpened = cv2.filter2D(image, -1, kernel_sharpen)
            
            # 混合原图和锐化图
            mask_3d = cv2.merge([mask, mask, mask]) / 255.0
            result = image * (1 - mask_3d * intensity) + sharpened * mask_3d * intensity
            
            return result.astype(np.uint8)
            
        except Exception as e:
            self.logger.error(f"眼部增强失败: {str(e)}")
            return image
    
    def teeth_whitening(self, image: np.ndarray, landmarks: List[Dict], intensity: float = 0.3) -> np.ndarray:
        """
        牙齿美白
        
        Args:
            image: 输入图像
            landmarks: 面部特征点
            intensity: 美白强度 (0.0-1.0)
            
        Returns:
            美白后的图像
        """
        try:
            if not landmarks or intensity <= 0:
                return image
            
            result = image.copy()
            points = landmarks[0]['landmarks']
            
            if len(points) < 68:
                return image
            
            # 嘴部区域 (points 48-67)
            mouth_points = np.array([[points[i]['x'], points[i]['y']] for i in range(48, 68)], np.int32)
            
            # 创建嘴部遮罩
            mask = np.zeros(image.shape[:2], dtype=np.uint8)
            cv2.fillPoly(mask, [mouth_points], 255)
            
            # 转换到HSV色彩空间
            hsv = cv2.cvtColor(result, cv2.COLOR_BGR2HSV)
            
            # 在嘴部区域增加亮度和降低饱和度
            mask_bool = mask > 0
            hsv[mask_bool, 2] = np.clip(hsv[mask_bool, 2] * (1 + intensity * 0.3), 0, 255)  # 增加亮度
            hsv[mask_bool, 1] = np.clip(hsv[mask_bool, 1] * (1 - intensity * 0.2), 0, 255)  # 降低饱和度
            
            # 转换回BGR
            result = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)
            
            return result
            
        except Exception as e:
            self.logger.error(f"牙齿美白失败: {str(e)}")
            return image
    
    def remove_blemishes(self, image: np.ndarray, intensity: float = 0.5) -> np.ndarray:
        """
        去除瑕疵
        
        Args:
            image: 输入图像
            intensity: 处理强度 (0.0-1.0)
            
        Returns:
            处理后的图像
        """
        try:
            if intensity <= 0:
                return image
            
            # 使用形态学操作去除小的瑕疵
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            
            # 开运算去除噪点
            opened = cv2.morphologyEx(image, cv2.MORPH_OPEN, kernel)
            
            # 混合原图和处理后的图像
            result = cv2.addWeighted(image, 1 - intensity, opened, intensity, 0)
            
            return result
            
        except Exception as e:
            self.logger.error(f"去除瑕疵失败: {str(e)}")
            return image
    
    def apply_beauty_filter(self, image: np.ndarray, landmarks: List[Dict], 
                          settings: Dict = None) -> Dict:
        """
        应用美颜滤镜
        
        Args:
            image: 输入图像
            landmarks: 面部特征点
            settings: 美颜设置
            
        Returns:
            美颜处理结果
        """
        start_time = time.time()
        
        try:
            if settings is None:
                settings = {
                    "skin_smoothing": 0.5,
                    "brightness": 10,
                    "contrast": 1.1,
                    "saturation": 1.2,
                    "eye_enhancement": 0.3,
                    "teeth_whitening": 0.3,
                    "blemish_removal": 0.4
                }
            
            result = image.copy()
            
            # 应用各种美颜效果
            if settings.get("skin_smoothing", 0) > 0:
                result = self.skin_smoothing(result, settings["skin_smoothing"])
            
            if settings.get("brightness", 0) != 0:
                result = self.brightness_adjustment(result, settings["brightness"])
            
            if settings.get("contrast", 1.0) != 1.0:
                result = self.contrast_adjustment(result, settings["contrast"])
            
            if settings.get("saturation", 1.0) != 1.0:
                result = self.saturation_adjustment(result, settings["saturation"])
            
            if settings.get("eye_enhancement", 0) > 0:
                result = self.eye_enhancement(result, landmarks, settings["eye_enhancement"])
            
            if settings.get("teeth_whitening", 0) > 0:
                result = self.teeth_whitening(result, landmarks, settings["teeth_whitening"])
            
            if settings.get("blemish_removal", 0) > 0:
                result = self.remove_blemishes(result, settings["blemish_removal"])
            
            processing_time = time.time() - start_time
            
            return {
                "success": True,
                "enhanced_image": result,
                "settings_applied": settings,
                "processing_time": round(processing_time, 3)
            }
            
        except Exception as e:
            self.logger.error(f"美颜滤镜应用失败: {str(e)}")
            return {
                "success": False,
                "enhanced_image": image,
                "error": str(e),
                "processing_time": time.time() - start_time
            }
