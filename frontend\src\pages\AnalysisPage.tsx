import React, { useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { useDropzone } from 'react-dropzone'
import { Upload, Image as ImageIcon, Loader2, Download, Eye } from 'lucide-react'
import toast from 'react-hot-toast'

const AnalysisPage = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysisResult, setAnalysisResult] = useState<any>(null)

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (file) {
      setSelectedFile(file)
      const url = URL.createObjectURL(file)
      setPreviewUrl(url)
      setAnalysisResult(null)
      toast.success('图片上传成功！')
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.bmp', '.tiff']
    },
    multiple: false,
    maxSize: 10 * 1024 * 1024 // 10MB
  })

  const handleAnalyze = async () => {
    if (!selectedFile) {
      toast.error('请先选择一张图片')
      return
    }

    setIsAnalyzing(true)
    
    try {
      const formData = new FormData()
      formData.append('file', selectedFile)

      const response = await fetch('/api/v1/analyze/complete', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error('分析失败')
      }

      const result = await response.json()
      setAnalysisResult(result)
      toast.success('分析完成！')
      
    } catch (error) {
      console.error('Analysis error:', error)
      toast.error('分析失败，请重试')
    } finally {
      setIsAnalyzing(false)
    }
  }

  const downloadResult = () => {
    if (analysisResult?.images?.result) {
      const link = document.createElement('a')
      link.href = analysisResult.images.result
      link.download = `analysis_result_${Date.now()}.jpg`
      link.click()
    }
  }

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h1 className="text-3xl font-bold text-slate-900 mb-4">
          图片分析
        </h1>
        <p className="text-lg text-slate-600">
          上传图片进行完整的人脸分析，包括人脸检测、表情分析、年龄性别预测等
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 左侧：图片上传和预览 */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-6"
        >
          {/* 上传区域 */}
          <div className="card p-6">
            <h2 className="text-xl font-semibold text-slate-900 mb-4">
              上传图片
            </h2>
            
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200 ${
                isDragActive
                  ? 'border-primary-500 bg-primary-50'
                  : 'border-slate-300 hover:border-primary-400 hover:bg-slate-50'
              }`}
            >
              <input {...getInputProps()} />
              <div className="space-y-4">
                <div className="mx-auto w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center">
                  <Upload className="w-8 h-8 text-slate-400" />
                </div>
                <div>
                  <p className="text-lg font-medium text-slate-900">
                    {isDragActive ? '释放文件到这里' : '拖拽图片到这里'}
                  </p>
                  <p className="text-sm text-slate-500 mt-1">
                    或点击选择文件 (支持 JPG, PNG, BMP, TIFF)
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 图片预览 */}
          {previewUrl && (
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-slate-900 mb-4">
                图片预览
              </h3>
              <div className="relative">
                <img
                  src={previewUrl}
                  alt="Preview"
                  className="w-full h-64 object-cover rounded-lg"
                />
                <div className="absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-sm">
                  {selectedFile?.name}
                </div>
              </div>
              
              <button
                onClick={handleAnalyze}
                disabled={isAnalyzing}
                className="btn-primary w-full mt-4 py-3"
              >
                {isAnalyzing ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    分析中...
                  </>
                ) : (
                  <>
                    <Eye className="w-5 h-5 mr-2" />
                    开始分析
                  </>
                )}
              </button>
            </div>
          )}
        </motion.div>

        {/* 右侧：分析结果 */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-6"
        >
          {analysisResult ? (
            <>
              {/* 分析统计 */}
              <div className="card p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-slate-900">
                    分析结果
                  </h3>
                  <button
                    onClick={downloadResult}
                    className="btn-secondary text-sm"
                  >
                    <Download className="w-4 h-4 mr-1" />
                    下载结果
                  </button>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {analysisResult.results?.statistics?.total_faces || 0}
                    </div>
                    <div className="text-sm text-blue-700">检测到的人脸</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {analysisResult.processing_time?.toFixed(2) || 0}s
                    </div>
                    <div className="text-sm text-green-700">处理时间</div>
                  </div>
                </div>
              </div>

              {/* 结果图片 */}
              {analysisResult.images?.result && (
                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-slate-900 mb-4">
                    分析结果图
                  </h3>
                  <img
                    src={analysisResult.images.result}
                    alt="Analysis Result"
                    className="w-full rounded-lg"
                  />
                </div>
              )}

              {/* 详细结果 */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-slate-900 mb-4">
                  详细信息
                </h3>
                <div className="space-y-4">
                  {analysisResult.results?.emotions?.map((emotion: any, index: number) => (
                    <div key={index} className="p-4 bg-slate-50 rounded-lg">
                      <div className="font-medium text-slate-900 mb-2">
                        人脸 {index + 1}
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-slate-600">表情：</span>
                          <span className="font-medium">{emotion.emotion_cn}</span>
                        </div>
                        <div>
                          <span className="text-slate-600">置信度：</span>
                          <span className="font-medium">{(emotion.confidence * 100).toFixed(1)}%</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          ) : (
            <div className="card p-12 text-center">
              <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <ImageIcon className="w-8 h-8 text-slate-400" />
              </div>
              <h3 className="text-lg font-medium text-slate-900 mb-2">
                等待分析
              </h3>
              <p className="text-slate-600">
                请上传图片并点击"开始分析"按钮
              </p>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  )
}

export default AnalysisPage
