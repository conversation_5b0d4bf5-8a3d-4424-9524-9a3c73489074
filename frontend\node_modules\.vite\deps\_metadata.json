{"hash": "24f46ab7", "configHash": "3d5ef13c", "lockfileHash": "18107297", "browserHash": "4c26ecb2", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "1a712ef4", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "d082d66a", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "137abe1d", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "c252bd72", "needsInterop": true}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "1bdf26f1", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "b56aae90", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "f3a83b41", "needsInterop": true}, "react-dropzone": {"src": "../../react-dropzone/dist/es/index.js", "file": "react-dropzone.js", "fileHash": "db6c1b4a", "needsInterop": false}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "94dc44ca", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "51e8ef01", "needsInterop": false}, "react-webcam": {"src": "../../react-webcam/dist/react-webcam.js", "file": "react-webcam.js", "fileHash": "8b394c96", "needsInterop": true}}, "chunks": {"chunk-SB5BK2J2": {"file": "chunk-SB5BK2J2.js"}, "chunk-N6MYFXC3": {"file": "chunk-N6MYFXC3.js"}}}