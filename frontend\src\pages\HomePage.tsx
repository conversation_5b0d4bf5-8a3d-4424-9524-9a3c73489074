import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { 
  Scan, 
  Video, 
  FolderOpen, 
  Database, 
  Brain,
  Zap,
  Shield,
  Smartphone,
  ArrowRight,
  Star,
  Users,
  Clock,
  Award
} from 'lucide-react'

const HomePage = () => {
  const features = [
    {
      icon: Scan,
      title: '智能人脸检测',
      description: '高精度人脸检测，支持多人脸同时识别',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Brain,
      title: '表情分析',
      description: '实时识别7种基本表情，准确率高达95%',
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: Users,
      title: '年龄性别预测',
      description: '智能预测年龄范围和性别，支持多种人群',
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: Shield,
      title: '人脸识别验证',
      description: '1:1和1:N人脸比对，安全可靠',
      color: 'from-orange-500 to-red-500'
    },
    {
      icon: Video,
      title: '实时视频分析',
      description: '支持摄像头实时分析，流畅无延迟',
      color: 'from-indigo-500 to-purple-500'
    },
    {
      icon: Zap,
      title: '批量处理',
      description: '支持多张图片批量分析，提高工作效率',
      color: 'from-yellow-500 to-orange-500'
    }
  ]

  const quickActions = [
    {
      title: '图片分析',
      description: '上传图片进行完整的人脸分析',
      icon: Scan,
      path: '/analysis',
      color: 'bg-gradient-to-br from-blue-500 to-cyan-500'
    },
    {
      title: '实时分析',
      description: '开启摄像头进行实时人脸分析',
      icon: Video,
      path: '/realtime',
      color: 'bg-gradient-to-br from-purple-500 to-pink-500'
    },
    {
      title: '批量处理',
      description: '批量上传多张图片进行分析',
      icon: FolderOpen,
      path: '/batch',
      color: 'bg-gradient-to-br from-green-500 to-emerald-500'
    },
    {
      title: '高级功能',
      description: '体验美颜滤镜和高级分析功能',
      icon: Brain,
      path: '/advanced',
      color: 'bg-gradient-to-br from-indigo-500 to-purple-500'
    },
    {
      title: '超级功能',
      description: '3D重建、情感AI、智能推荐',
      icon: Zap,
      path: '/super-advanced',
      color: 'bg-gradient-to-br from-yellow-500 to-orange-500'
    },
    {
      title: '数据分析',
      description: '查看系统使用统计和分析报告',
      icon: Users,
      path: '/analytics',
      color: 'bg-gradient-to-br from-pink-500 to-rose-500'
    },
    {
      title: '人脸数据库',
      description: '管理已知人脸数据库',
      icon: Database,
      path: '/database',
      color: 'bg-gradient-to-br from-orange-500 to-red-500'
    }
  ]

  const stats = [
    { label: '检测准确率', value: '99.5%', icon: Award },
    { label: '处理速度', value: '<100ms', icon: Clock },
    { label: '支持格式', value: '5+', icon: Smartphone },
    { label: '用户评分', value: '4.9/5', icon: Star }
  ]

  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <section className="text-center py-16">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-6"
        >
          <div className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-4">
            <Star className="w-4 h-4 mr-2" />
            AI驱动的人脸分析技术
          </div>
          
          <h1 className="text-5xl md:text-6xl font-bold text-slate-900 leading-tight">
            智能
            <span className="gradient-text"> 人脸分析 </span>
            系统
          </h1>
          
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            基于先进的AI技术，提供人脸检测、表情分析、年龄性别预测、身份识别等全方位的人脸分析服务。
            界面美观，功能强大，操作简单。
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8">
            <Link
              to="/analysis"
              className="btn-primary px-8 py-4 text-lg font-semibold group"
            >
              开始分析
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
            </Link>
            <Link
              to="/about"
              className="btn-secondary px-8 py-4 text-lg font-semibold"
            >
              了解更多
            </Link>
          </div>
        </motion.div>
      </section>

      {/* Stats Section */}
      <section className="py-12">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="card text-center p-6"
              >
                <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-100 text-primary-600 rounded-lg mb-4">
                  <Icon className="w-6 h-6" />
                </div>
                <div className="text-2xl font-bold text-slate-900 mb-1">
                  {stat.value}
                </div>
                <div className="text-sm text-slate-600">
                  {stat.label}
                </div>
              </motion.div>
            )
          })}
        </div>
      </section>

      {/* Quick Actions */}
      <section className="py-12">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-bold text-slate-900 mb-4">
            快速开始
          </h2>
          <p className="text-lg text-slate-600">
            选择您需要的功能，立即开始人脸分析
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {quickActions.map((action, index) => {
            const Icon = action.icon
            return (
              <motion.div
                key={action.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Link
                  to={action.path}
                  className="card-hover p-6 block group h-full"
                >
                  <div className={`inline-flex items-center justify-center w-12 h-12 ${action.color} text-white rounded-lg mb-4 group-hover:scale-110 transition-transform duration-200`}>
                    <Icon className="w-6 h-6" />
                  </div>
                  <h3 className="text-lg font-semibold text-slate-900 mb-2">
                    {action.title}
                  </h3>
                  <p className="text-slate-600 text-sm">
                    {action.description}
                  </p>
                  <div className="flex items-center text-primary-600 text-sm font-medium mt-4 group-hover:translate-x-1 transition-transform duration-200">
                    立即使用
                    <ArrowRight className="w-4 h-4 ml-1" />
                  </div>
                </Link>
              </motion.div>
            )
          })}
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-bold text-slate-900 mb-4">
            核心功能
          </h2>
          <p className="text-lg text-slate-600">
            基于最新AI技术的全方位人脸分析解决方案
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon
            return (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="card p-6 group hover:shadow-large transition-all duration-300"
              >
                <div className={`inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br ${feature.color} text-white rounded-lg mb-4 group-hover:scale-110 transition-transform duration-200`}>
                  <Icon className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-semibold text-slate-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-slate-600 leading-relaxed">
                  {feature.description}
                </p>
              </motion.div>
            )
          })}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="card p-12 text-center bg-gradient-to-br from-primary-50 to-blue-50 border-primary-200"
        >
          <h2 className="text-3xl font-bold text-slate-900 mb-4">
            准备开始了吗？
          </h2>
          <p className="text-lg text-slate-600 mb-8 max-w-2xl mx-auto">
            立即体验我们的人脸分析系统，感受AI技术带来的便利和准确性。
            无需注册，即刻开始使用。
          </p>
          <Link
            to="/analysis"
            className="btn-primary px-8 py-4 text-lg font-semibold group inline-flex items-center"
          >
            立即开始分析
            <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
          </Link>
        </motion.div>
      </section>
    </div>
  )
}

export default HomePage
