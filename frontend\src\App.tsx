import React from 'react'
import { Routes, Route } from 'react-router-dom'
import { motion } from 'framer-motion'

// 组件导入
import Navbar from './components/Navbar'
import HomePage from './pages/HomePage'
import AnalysisPage from './pages/AnalysisPage'
import RealtimePage from './pages/RealtimePage'
import BatchPage from './pages/BatchPage'
import AdvancedPage from './pages/AdvancedPage'
import SuperAdvancedPage from './pages/SuperAdvancedPage'
import AnalyticsPage from './pages/AnalyticsPage'
import DatabasePage from './pages/DatabasePage'
import AboutPage from './pages/AboutPage'

function App() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 背景装饰 */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-green-400/20 to-blue-400/20 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
      </div>

      {/* 主要内容 */}
      <div className="relative z-10">
        <Navbar />
        
        <motion.main
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="container mx-auto px-4 py-8"
        >
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/analysis" element={<AnalysisPage />} />
            <Route path="/realtime" element={<RealtimePage />} />
            <Route path="/batch" element={<BatchPage />} />
            <Route path="/advanced" element={<AdvancedPage />} />
            <Route path="/super-advanced" element={<SuperAdvancedPage />} />
            <Route path="/analytics" element={<AnalyticsPage />} />
            <Route path="/database" element={<DatabasePage />} />
            <Route path="/about" element={<AboutPage />} />
          </Routes>
        </motion.main>
      </div>
    </div>
  )
}

export default App
