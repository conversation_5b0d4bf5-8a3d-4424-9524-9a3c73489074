import React, { useState, useRef, useCallback } from 'react'
import { motion } from 'framer-motion'
import Webcam from 'react-webcam'
import { Video, VideoOff, Play, Square, Settings, Camera } from 'lucide-react'
import toast from 'react-hot-toast'

const RealtimePage = () => {
  const webcamRef = useRef<Webcam>(null)
  const [isStreaming, setIsStreaming] = useState(false)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysisResults, setAnalysisResults] = useState<any[]>([])
  const [deviceId, setDeviceId] = useState<string>('')
  const [devices, setDevices] = useState<MediaDeviceInfo[]>([])

  // 获取可用的摄像头设备
  const handleDevices = useCallback(
    (mediaDevices: MediaDeviceInfo[]) => {
      const videoDevices = mediaDevices.filter(({ kind }) => kind === "videoinput")
      setDevices(videoDevices)
      if (videoDevices.length > 0 && !deviceId) {
        setDeviceId(videoDevices[0].deviceId)
      }
    },
    [deviceId]
  )

  React.useEffect(() => {
    navigator.mediaDevices.enumerateDevices().then(handleDevices)
  }, [handleDevices])

  const startStreaming = () => {
    setIsStreaming(true)
    toast.success('摄像头已启动')
  }

  const stopStreaming = () => {
    setIsStreaming(false)
    setIsAnalyzing(false)
    toast.success('摄像头已关闭')
  }

  const startAnalysis = () => {
    if (!isStreaming) {
      toast.error('请先启动摄像头')
      return
    }
    setIsAnalyzing(true)
    toast.success('开始实时分析')
    
    // 模拟实时分析结果
    const interval = setInterval(() => {
      const mockResult = {
        timestamp: new Date().toLocaleTimeString(),
        faces: Math.floor(Math.random() * 3) + 1,
        emotions: ['happy', 'neutral', 'surprise'][Math.floor(Math.random() * 3)],
        confidence: (Math.random() * 0.3 + 0.7).toFixed(2)
      }
      
      setAnalysisResults(prev => [mockResult, ...prev.slice(0, 9)]) // 保留最近10条记录
    }, 2000)

    // 清理函数
    return () => clearInterval(interval)
  }

  const stopAnalysis = () => {
    setIsAnalyzing(false)
    toast.success('停止实时分析')
  }

  const capturePhoto = useCallback(() => {
    if (webcamRef.current) {
      const imageSrc = webcamRef.current.getScreenshot()
      if (imageSrc) {
        // 这里可以将截图发送到后端进行分析
        toast.success('照片已捕获')
      }
    }
  }, [webcamRef])

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h1 className="text-3xl font-bold text-slate-900 mb-4">
          实时分析
        </h1>
        <p className="text-lg text-slate-600">
          使用摄像头进行实时人脸分析，支持表情识别、年龄性别预测等功能
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 左侧：摄像头控制 */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="lg:col-span-2 space-y-6"
        >
          {/* 摄像头画面 */}
          <div className="card p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-slate-900">
                摄像头画面
              </h2>
              <div className="flex items-center space-x-2">
                {isStreaming && (
                  <div className="flex items-center space-x-1 text-green-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-sm font-medium">直播中</span>
                  </div>
                )}
              </div>
            </div>

            <div className="relative bg-slate-900 rounded-lg overflow-hidden aspect-video">
              {isStreaming ? (
                <Webcam
                  ref={webcamRef}
                  audio={false}
                  videoConstraints={{
                    deviceId: deviceId,
                    width: 1280,
                    height: 720,
                  }}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center text-white">
                    <VideoOff className="w-16 h-16 mx-auto mb-4 opacity-50" />
                    <p className="text-lg font-medium">摄像头未启动</p>
                    <p className="text-sm opacity-75">点击下方按钮开始</p>
                  </div>
                </div>
              )}
              
              {/* 分析状态覆盖层 */}
              {isAnalyzing && (
                <div className="absolute top-4 left-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  正在分析...
                </div>
              )}
            </div>
          </div>

          {/* 控制按钮 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">
              控制面板
            </h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {!isStreaming ? (
                <button
                  onClick={startStreaming}
                  className="btn-success flex items-center justify-center py-3"
                >
                  <Video className="w-5 h-5 mr-2" />
                  启动摄像头
                </button>
              ) : (
                <button
                  onClick={stopStreaming}
                  className="btn-error flex items-center justify-center py-3"
                >
                  <VideoOff className="w-5 h-5 mr-2" />
                  关闭摄像头
                </button>
              )}

              {!isAnalyzing ? (
                <button
                  onClick={startAnalysis}
                  disabled={!isStreaming}
                  className="btn-primary flex items-center justify-center py-3"
                >
                  <Play className="w-5 h-5 mr-2" />
                  开始分析
                </button>
              ) : (
                <button
                  onClick={stopAnalysis}
                  className="btn-warning flex items-center justify-center py-3"
                >
                  <Square className="w-5 h-5 mr-2" />
                  停止分析
                </button>
              )}

              <button
                onClick={capturePhoto}
                disabled={!isStreaming}
                className="btn-secondary flex items-center justify-center py-3"
              >
                <Camera className="w-5 h-5 mr-2" />
                拍照
              </button>

              <button className="btn-secondary flex items-center justify-center py-3">
                <Settings className="w-5 h-5 mr-2" />
                设置
              </button>
            </div>

            {/* 摄像头选择 */}
            {devices.length > 1 && (
              <div className="mt-4">
                <label className="label">选择摄像头</label>
                <select
                  value={deviceId}
                  onChange={(e) => setDeviceId(e.target.value)}
                  className="input"
                >
                  {devices.map((device) => (
                    <option key={device.deviceId} value={device.deviceId}>
                      {device.label || `摄像头 ${device.deviceId.slice(0, 8)}`}
                    </option>
                  ))}
                </select>
              </div>
            )}
          </div>
        </motion.div>

        {/* 右侧：实时结果 */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-6"
        >
          {/* 实时统计 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">
              实时统计
            </h3>
            
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-slate-600">状态</span>
                <span className={`badge ${isAnalyzing ? 'badge-success' : 'badge-secondary'}`}>
                  {isAnalyzing ? '分析中' : '待机'}
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-slate-600">检测帧数</span>
                <span className="font-medium">{analysisResults.length}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-slate-600">平均置信度</span>
                <span className="font-medium">
                  {analysisResults.length > 0 
                    ? (analysisResults.reduce((sum, r) => sum + parseFloat(r.confidence), 0) / analysisResults.length).toFixed(2)
                    : '0.00'
                  }
                </span>
              </div>
            </div>
          </div>

          {/* 分析历史 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">
              分析历史
            </h3>
            
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {analysisResults.length > 0 ? (
                analysisResults.map((result, index) => (
                  <div key={index} className="p-3 bg-slate-50 rounded-lg text-sm">
                    <div className="flex justify-between items-center mb-1">
                      <span className="font-medium">{result.timestamp}</span>
                      <span className="text-slate-500">{result.faces} 人脸</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-600">表情: {result.emotions}</span>
                      <span className="text-slate-600">置信度: {result.confidence}</span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center text-slate-500 py-8">
                  <Video className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>暂无分析记录</p>
                  <p className="text-xs">开始分析后将显示结果</p>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default RealtimePage
