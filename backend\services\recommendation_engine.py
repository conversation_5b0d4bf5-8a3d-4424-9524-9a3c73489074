"""
智能推荐引擎
Intelligent Recommendation Engine
"""

import json
import random
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, Counter

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from utils.logger import app_logger
from utils.config import settings

class RecommendationEngine:
    """智能推荐引擎"""
    
    def __init__(self):
        """初始化推荐引擎"""
        self.logger = app_logger
        
        # 美颜推荐规则
        self.beauty_recommendations = {
            'age_based': {
                '0-18': {
                    'skin_smoothing': 0.3,
                    'brightness': 5,
                    'contrast': 1.05,
                    'saturation': 1.1,
                    'eye_enhancement': 0.2,
                    'teeth_whitening': 0.1,
                    'blemish_removal': 0.3
                },
                '19-30': {
                    'skin_smoothing': 0.4,
                    'brightness': 8,
                    'contrast': 1.1,
                    'saturation': 1.15,
                    'eye_enhancement': 0.3,
                    'teeth_whitening': 0.2,
                    'blemish_removal': 0.4
                },
                '31-45': {
                    'skin_smoothing': 0.6,
                    'brightness': 12,
                    'contrast': 1.15,
                    'saturation': 1.2,
                    'eye_enhancement': 0.4,
                    'teeth_whitening': 0.3,
                    'blemish_removal': 0.5
                },
                '46+': {
                    'skin_smoothing': 0.7,
                    'brightness': 15,
                    'contrast': 1.2,
                    'saturation': 1.25,
                    'eye_enhancement': 0.5,
                    'teeth_whitening': 0.4,
                    'blemish_removal': 0.6
                }
            },
            'emotion_based': {
                'happy': {
                    'brightness': 1.2,
                    'saturation': 1.3,
                    'eye_enhancement': 1.5
                },
                'sad': {
                    'brightness': 1.4,
                    'skin_smoothing': 1.3,
                    'blemish_removal': 1.2
                },
                'angry': {
                    'skin_smoothing': 1.4,
                    'blemish_removal': 1.3,
                    'brightness': 1.1
                },
                'neutral': {
                    'brightness': 1.1,
                    'contrast': 1.1,
                    'saturation': 1.1
                }
            },
            'skin_tone_based': {
                'fair': {
                    'brightness': 0.9,
                    'contrast': 1.1,
                    'saturation': 1.2
                },
                'light': {
                    'brightness': 1.0,
                    'contrast': 1.05,
                    'saturation': 1.15
                },
                'medium': {
                    'brightness': 1.1,
                    'contrast': 1.0,
                    'saturation': 1.1
                },
                'dark': {
                    'brightness': 1.2,
                    'contrast': 0.95,
                    'saturation': 1.05
                }
            }
        }
        
        # 拍照建议
        self.photo_suggestions = {
            'lighting': {
                'indoor': ['使用自然光源', '避免强烈顶光', '可以使用补光灯'],
                'outdoor': ['选择柔和光线时段', '避免正午强光', '利用阴影区域'],
                'low_light': ['增加光源', '使用反光板', '调整相机设置']
            },
            'pose': {
                'front': ['保持自然微笑', '眼神看向镜头', '肩膀略微倾斜'],
                'side': ['突出轮廓线条', '注意下巴角度', '保持优雅姿态'],
                'three_quarter': ['展现立体感', '注意光影效果', '突出最佳角度']
            },
            'composition': {
                'close_up': ['注意面部细节', '确保清晰对焦', '考虑背景虚化'],
                'medium': ['展现上半身', '注意手部姿态', '保持整体协调'],
                'full_body': ['注意整体比例', '选择合适背景', '展现完整形象']
            }
        }
        
        self.logger.info("智能推荐引擎初始化完成")
    
    def recommend_beauty_settings(self, analysis_result: Dict) -> Dict:
        """
        推荐美颜设置
        
        Args:
            analysis_result: 面部分析结果
            
        Returns:
            推荐的美颜设置
        """
        try:
            # 基础设置
            base_settings = {
                'skin_smoothing': 0.5,
                'brightness': 10,
                'contrast': 1.1,
                'saturation': 1.2,
                'eye_enhancement': 0.3,
                'teeth_whitening': 0.3,
                'blemish_removal': 0.4
            }
            
            recommendations = []
            
            # 基于年龄的推荐
            age_gender = analysis_result.get('age_gender', [])
            if age_gender:
                age = age_gender[0].get('age', {}).get('age', 25)
                age_group = self._get_age_group(age)
                age_settings = self.beauty_recommendations['age_based'].get(age_group, {})
                
                for key, value in age_settings.items():
                    base_settings[key] = value
                
                recommendations.append(f"基于年龄 {age} 岁的推荐设置")
            
            # 基于情感的推荐
            emotions = analysis_result.get('emotions', [])
            if emotions:
                emotion = emotions[0].get('emotion', 'neutral')
                emotion_modifiers = self.beauty_recommendations['emotion_based'].get(emotion, {})
                
                for key, modifier in emotion_modifiers.items():
                    if key in base_settings:
                        base_settings[key] *= modifier
                
                recommendations.append(f"基于 {emotion} 情感的调整")
            
            # 基于肤色的推荐
            advanced_analysis = analysis_result.get('advanced_analysis', [])
            if advanced_analysis:
                skin_tone = advanced_analysis[0].get('skin_tone', {})
                tone_category = skin_tone.get('tone_category', 'medium')
                tone_settings = self.beauty_recommendations['skin_tone_based'].get(tone_category, {})
                
                for key, modifier in tone_settings.items():
                    if key in base_settings:
                        base_settings[key] *= modifier
                
                recommendations.append(f"基于 {skin_tone.get('skin_tone', '中等')} 肤色的调整")
            
            # 限制数值范围
            base_settings = self._clamp_settings(base_settings)
            
            return {
                "success": True,
                "recommended_settings": base_settings,
                "recommendations": recommendations,
                "confidence": self._calculate_recommendation_confidence(analysis_result)
            }
            
        except Exception as e:
            self.logger.error(f"美颜设置推荐失败: {str(e)}")
            return {"error": str(e)}
    
    def _get_age_group(self, age: int) -> str:
        """获取年龄组"""
        if age <= 18:
            return '0-18'
        elif age <= 30:
            return '19-30'
        elif age <= 45:
            return '31-45'
        else:
            return '46+'
    
    def _clamp_settings(self, settings: Dict) -> Dict:
        """限制设置值范围"""
        limits = {
            'skin_smoothing': (0.0, 1.0),
            'brightness': (-50, 50),
            'contrast': (0.5, 2.0),
            'saturation': (0.5, 2.0),
            'eye_enhancement': (0.0, 1.0),
            'teeth_whitening': (0.0, 1.0),
            'blemish_removal': (0.0, 1.0)
        }
        
        clamped = {}
        for key, value in settings.items():
            if key in limits:
                min_val, max_val = limits[key]
                clamped[key] = max(min_val, min(max_val, value))
            else:
                clamped[key] = value
        
        return clamped
    
    def _calculate_recommendation_confidence(self, analysis_result: Dict) -> float:
        """计算推荐置信度"""
        confidence_factors = []
        
        # 检查数据完整性
        if analysis_result.get('age_gender'):
            confidence_factors.append(0.3)
        if analysis_result.get('emotions'):
            confidence_factors.append(0.3)
        if analysis_result.get('advanced_analysis'):
            confidence_factors.append(0.4)
        
        return sum(confidence_factors)
    
    def suggest_photo_improvements(self, analysis_result: Dict) -> Dict:
        """
        建议拍照改进
        
        Args:
            analysis_result: 面部分析结果
            
        Returns:
            拍照改进建议
        """
        try:
            suggestions = []
            
            # 基于面部对称性的建议
            advanced_analysis = analysis_result.get('advanced_analysis', [])
            if advanced_analysis:
                symmetry = advanced_analysis[0].get('symmetry', {})
                symmetry_score = symmetry.get('symmetry_score', 0)
                
                if symmetry_score < 0.7:
                    suggestions.append({
                        "category": "角度调整",
                        "suggestion": "尝试稍微调整拍照角度，可能会获得更好的对称效果",
                        "priority": "medium"
                    })
            
            # 基于情感的建议
            emotions = analysis_result.get('emotions', [])
            if emotions:
                emotion = emotions[0].get('emotion', 'neutral')
                confidence = emotions[0].get('confidence', 0)
                
                if emotion == 'sad' or emotion == 'angry':
                    suggestions.append({
                        "category": "表情调整",
                        "suggestion": "尝试放松面部肌肉，展现更自然的表情",
                        "priority": "high"
                    })
                elif emotion == 'neutral' and confidence > 0.8:
                    suggestions.append({
                        "category": "表情建议",
                        "suggestion": "可以尝试轻微的微笑，会让照片更有亲和力",
                        "priority": "low"
                    })
            
            # 基于头部姿态的建议
            pose_analysis = analysis_result.get('pose_estimation', {})
            if pose_analysis.get('success'):
                euler_angles = pose_analysis.get('euler_angles', {})
                yaw = abs(euler_angles.get('yaw', 0))
                pitch = abs(euler_angles.get('pitch', 0))
                
                if yaw > 20:
                    suggestions.append({
                        "category": "头部角度",
                        "suggestion": "头部转向角度较大，可以尝试更正面的角度",
                        "priority": "medium"
                    })
                
                if pitch > 15:
                    suggestions.append({
                        "category": "头部姿态",
                        "suggestion": "头部俯仰角度较大，建议调整到更自然的位置",
                        "priority": "medium"
                    })
            
            # 通用拍照建议
            general_suggestions = [
                {
                    "category": "光线",
                    "suggestion": "确保光线充足且均匀，避免强烈的阴影",
                    "priority": "high"
                },
                {
                    "category": "背景",
                    "suggestion": "选择简洁的背景，避免杂乱元素干扰",
                    "priority": "medium"
                },
                {
                    "category": "距离",
                    "suggestion": "保持适当的拍摄距离，确保面部清晰可见",
                    "priority": "medium"
                }
            ]
            
            # 随机选择一些通用建议
            suggestions.extend(random.sample(general_suggestions, 2))
            
            return {
                "success": True,
                "suggestions": suggestions,
                "total_suggestions": len(suggestions)
            }
            
        except Exception as e:
            self.logger.error(f"拍照改进建议失败: {str(e)}")
            return {"error": str(e)}
    
    def recommend_makeup_style(self, analysis_result: Dict) -> Dict:
        """
        推荐化妆风格
        
        Args:
            analysis_result: 面部分析结果
            
        Returns:
            化妆风格推荐
        """
        try:
            recommendations = []
            
            # 基于面部形状的推荐
            advanced_analysis = analysis_result.get('advanced_analysis', [])
            if advanced_analysis:
                face_shape = advanced_analysis[0].get('face_shape', {}).get('face_shape', '椭圆形')
                
                shape_recommendations = {
                    '圆形': {
                        'style': '修容妆',
                        'tips': ['使用阴影粉修饰脸型', '强调眼妆', '选择偏深色唇妆'],
                        'avoid': ['过于圆润的腮红', '太宽的眉形']
                    },
                    '方形': {
                        'style': '柔和妆',
                        'tips': ['柔化棱角', '使用圆润腮红', '选择柔和色彩'],
                        'avoid': ['过于锐利的眼线', '方形眉毛']
                    },
                    '长形': {
                        'style': '横向拉宽妆',
                        'tips': ['横向腮红', '强调眼妆宽度', '丰满唇妆'],
                        'avoid': ['过长的眼线', '过细的眉毛']
                    },
                    '心形': {
                        'style': '平衡妆',
                        'tips': ['强调下半部分', '丰满唇妆', '柔和眉形'],
                        'avoid': ['过重的上眼妆', '过宽的眉毛']
                    },
                    '椭圆形': {
                        'style': '经典妆',
                        'tips': ['适合各种妆容', '可以尝试不同风格', '突出最佳特征'],
                        'avoid': ['过于夸张的妆容']
                    }
                }
                
                face_rec = shape_recommendations.get(face_shape, shape_recommendations['椭圆形'])
                recommendations.append({
                    "category": "面部形状",
                    "face_shape": face_shape,
                    "recommended_style": face_rec['style'],
                    "tips": face_rec['tips'],
                    "avoid": face_rec['avoid']
                })
            
            # 基于肤色的推荐
            if advanced_analysis:
                skin_tone = advanced_analysis[0].get('skin_tone', {})
                tone_category = skin_tone.get('tone_category', 'medium')
                
                tone_recommendations = {
                    'fair': {
                        'colors': ['粉色系', '珊瑚色', '浅紫色'],
                        'foundation': '选择偏粉调的粉底',
                        'tips': ['避免过深的颜色', '可以尝试清透妆感']
                    },
                    'light': {
                        'colors': ['桃色系', '橘色系', '浅棕色'],
                        'foundation': '选择自然调的粉底',
                        'tips': ['适合自然妆容', '可以尝试日常妆']
                    },
                    'medium': {
                        'colors': ['棕色系', '橘红色', '深粉色'],
                        'foundation': '选择黄调或橄榄调粉底',
                        'tips': ['适合浓郁色彩', '可以尝试烟熏妆']
                    },
                    'dark': {
                        'colors': ['深色系', '金色系', '紫红色'],
                        'foundation': '选择深色调粉底',
                        'tips': ['适合大胆色彩', '可以突出眼部轮廓']
                    }
                }
                
                tone_rec = tone_recommendations.get(tone_category, tone_recommendations['medium'])
                recommendations.append({
                    "category": "肤色搭配",
                    "skin_tone": skin_tone.get('skin_tone', '中等'),
                    "recommended_colors": tone_rec['colors'],
                    "foundation_tip": tone_rec['foundation'],
                    "tips": tone_rec['tips']
                })
            
            # 基于年龄的推荐
            age_gender = analysis_result.get('age_gender', [])
            if age_gender:
                age = age_gender[0].get('age', {}).get('age', 25)
                
                if age < 25:
                    age_rec = {
                        'style': '清新自然妆',
                        'focus': ['突出年轻活力', '清透底妆', '自然眉形'],
                        'colors': ['清新色彩', '粉嫩色系']
                    }
                elif age < 35:
                    age_rec = {
                        'style': '职场精致妆',
                        'focus': ['专业形象', '精致眼妆', '自然轮廓'],
                        'colors': ['经典色彩', '大地色系']
                    }
                else:
                    age_rec = {
                        'style': '优雅成熟妆',
                        'focus': ['提升气质', '修饰细纹', '突出优势'],
                        'colors': ['成熟色彩', '深色系']
                    }
                
                recommendations.append({
                    "category": "年龄适配",
                    "age_group": f"{age}岁",
                    "recommended_style": age_rec['style'],
                    "focus_points": age_rec['focus'],
                    "color_palette": age_rec['colors']
                })
            
            return {
                "success": True,
                "makeup_recommendations": recommendations,
                "total_recommendations": len(recommendations)
            }
            
        except Exception as e:
            self.logger.error(f"化妆风格推荐失败: {str(e)}")
            return {"error": str(e)}
    
    def comprehensive_recommendations(self, analysis_result: Dict) -> Dict:
        """
        综合推荐
        
        Args:
            analysis_result: 面部分析结果
            
        Returns:
            综合推荐结果
        """
        start_time = datetime.now()
        
        try:
            results = {
                "beauty_settings": self.recommend_beauty_settings(analysis_result),
                "photo_improvements": self.suggest_photo_improvements(analysis_result),
                "makeup_style": self.recommend_makeup_style(analysis_result),
                "timestamp": start_time.isoformat()
            }
            
            processing_time = (datetime.now() - start_time).total_seconds()
            results["processing_time"] = round(processing_time, 3)
            
            self.logger.info(f"综合推荐完成，耗时 {processing_time:.3f}s")
            
            return results
            
        except Exception as e:
            self.logger.error(f"综合推荐失败: {str(e)}")
            return {
                "error": str(e),
                "processing_time": (datetime.now() - start_time).total_seconds()
            }
