"""
人脸识别服务
Face Recognition Service
"""

import cv2
import numpy as np
import face_recognition
from typing import List, Dict, Tuple, Optional
import time
import pickle
import os
from pathlib import Path

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from utils.logger import app_logger
from utils.config import settings

class FaceRecognitionService:
    """人脸识别服务"""
    
    def __init__(self):
        """初始化人脸识别服务"""
        self.logger = app_logger
        self.tolerance = settings.FACE_RECOGNITION_TOLERANCE
        self.model = settings.FACE_ENCODING_MODEL
        
        # 已知人脸数据库
        self.known_face_encodings = []
        self.known_face_names = []
        self.known_face_metadata = []
        
        # 数据库文件路径
        self.database_path = "face_database.pkl"
        
        # 加载已知人脸数据库
        self._load_face_database()
        
        self.logger.info("人脸识别服务初始化完成")
    
    def _load_face_database(self):
        """加载人脸数据库"""
        try:
            if os.path.exists(self.database_path):
                with open(self.database_path, 'rb') as f:
                    data = pickle.load(f)
                    self.known_face_encodings = data.get('encodings', [])
                    self.known_face_names = data.get('names', [])
                    self.known_face_metadata = data.get('metadata', [])
                
                self.logger.info(f"成功加载人脸数据库，包含 {len(self.known_face_names)} 个已知人脸")
            else:
                self.logger.info("人脸数据库文件不存在，创建新的数据库")
                
        except Exception as e:
            self.logger.error(f"加载人脸数据库失败: {str(e)}")
            self.known_face_encodings = []
            self.known_face_names = []
            self.known_face_metadata = []
    
    def _save_face_database(self):
        """保存人脸数据库"""
        try:
            data = {
                'encodings': self.known_face_encodings,
                'names': self.known_face_names,
                'metadata': self.known_face_metadata
            }
            
            with open(self.database_path, 'wb') as f:
                pickle.dump(data, f)
            
            self.logger.info(f"成功保存人脸数据库，包含 {len(self.known_face_names)} 个人脸")
            
        except Exception as e:
            self.logger.error(f"保存人脸数据库失败: {str(e)}")
    
    def encode_face(self, face_image: np.ndarray) -> Optional[np.ndarray]:
        """
        对人脸图像进行编码
        
        Args:
            face_image: 人脸图像 (BGR格式)
            
        Returns:
            人脸编码向量，如果失败返回None
        """
        try:
            # 转换为RGB格式
            rgb_image = cv2.cvtColor(face_image, cv2.COLOR_BGR2RGB)
            
            # 检测人脸位置
            face_locations = face_recognition.face_locations(rgb_image, model="hog")
            
            if not face_locations:
                self.logger.warning("未检测到人脸，无法进行编码")
                return None
            
            # 获取人脸编码
            face_encodings = face_recognition.face_encodings(
                rgb_image, 
                face_locations, 
                model=self.model
            )
            
            if face_encodings:
                return face_encodings[0]
            else:
                self.logger.warning("人脸编码失败")
                return None
                
        except Exception as e:
            self.logger.error(f"人脸编码失败: {str(e)}")
            return None
    
    def add_known_face(self, face_image: np.ndarray, name: str, 
                      metadata: Optional[Dict] = None) -> bool:
        """
        添加已知人脸到数据库
        
        Args:
            face_image: 人脸图像
            name: 人脸对应的姓名
            metadata: 额外的元数据
            
        Returns:
            是否添加成功
        """
        try:
            # 对人脸进行编码
            face_encoding = self.encode_face(face_image)
            
            if face_encoding is None:
                return False
            
            # 检查是否已存在相同的人脸
            if self.known_face_encodings:
                matches = face_recognition.compare_faces(
                    self.known_face_encodings, 
                    face_encoding, 
                    tolerance=self.tolerance
                )
                
                if any(matches):
                    self.logger.warning(f"人脸 {name} 可能已存在于数据库中")
            
            # 添加到数据库
            self.known_face_encodings.append(face_encoding)
            self.known_face_names.append(name)
            self.known_face_metadata.append(metadata or {})
            
            # 保存数据库
            self._save_face_database()
            
            self.logger.info(f"成功添加人脸: {name}")
            return True
            
        except Exception as e:
            self.logger.error(f"添加已知人脸失败: {str(e)}")
            return False
    
    def recognize_face(self, face_image: np.ndarray) -> Dict:
        """
        识别人脸
        
        Args:
            face_image: 人脸图像
            
        Returns:
            识别结果
        """
        start_time = time.time()
        
        try:
            # 对输入人脸进行编码
            face_encoding = self.encode_face(face_image)
            
            if face_encoding is None:
                return {
                    'recognized': False,
                    'name': 'unknown',
                    'confidence': 0.0,
                    'distance': float('inf'),
                    'processing_time': time.time() - start_time,
                    'error': '无法检测到人脸或编码失败'
                }
            
            # 如果数据库为空
            if not self.known_face_encodings:
                return {
                    'recognized': False,
                    'name': 'unknown',
                    'confidence': 0.0,
                    'distance': float('inf'),
                    'processing_time': time.time() - start_time,
                    'message': '人脸数据库为空'
                }
            
            # 计算与已知人脸的距离
            face_distances = face_recognition.face_distance(
                self.known_face_encodings, 
                face_encoding
            )
            
            # 找到最小距离
            min_distance_index = np.argmin(face_distances)
            min_distance = face_distances[min_distance_index]
            
            # 判断是否匹配
            is_match = min_distance <= self.tolerance
            
            processing_time = time.time() - start_time
            
            if is_match:
                name = self.known_face_names[min_distance_index]
                metadata = self.known_face_metadata[min_distance_index]
                confidence = 1.0 - min_distance  # 转换为置信度
                
                result = {
                    'recognized': True,
                    'name': name,
                    'confidence': confidence,
                    'distance': min_distance,
                    'metadata': metadata,
                    'processing_time': processing_time
                }
                
                self.logger.info(f"识别成功: {name} (置信度: {confidence:.3f})")
            else:
                result = {
                    'recognized': False,
                    'name': 'unknown',
                    'confidence': 0.0,
                    'distance': min_distance,
                    'processing_time': processing_time,
                    'message': f'未找到匹配的人脸 (最小距离: {min_distance:.3f})'
                }
                
                self.logger.info(f"识别失败: 未找到匹配的人脸")
            
            return result
            
        except Exception as e:
            self.logger.error(f"人脸识别失败: {str(e)}")
            return {
                'recognized': False,
                'name': 'error',
                'confidence': 0.0,
                'distance': float('inf'),
                'processing_time': time.time() - start_time,
                'error': str(e)
            }
    
    def compare_faces(self, face_image1: np.ndarray, face_image2: np.ndarray) -> Dict:
        """
        比较两张人脸图像
        
        Args:
            face_image1: 第一张人脸图像
            face_image2: 第二张人脸图像
            
        Returns:
            比较结果
        """
        start_time = time.time()
        
        try:
            # 对两张人脸进行编码
            encoding1 = self.encode_face(face_image1)
            encoding2 = self.encode_face(face_image2)
            
            if encoding1 is None or encoding2 is None:
                return {
                    'is_same_person': False,
                    'confidence': 0.0,
                    'distance': float('inf'),
                    'processing_time': time.time() - start_time,
                    'error': '无法对一张或两张人脸进行编码'
                }
            
            # 计算距离
            distance = face_recognition.face_distance([encoding1], encoding2)[0]
            
            # 判断是否为同一人
            is_same = distance <= self.tolerance
            confidence = 1.0 - distance
            
            processing_time = time.time() - start_time
            
            result = {
                'is_same_person': is_same,
                'confidence': confidence,
                'distance': distance,
                'processing_time': processing_time
            }
            
            self.logger.info(f"人脸比较完成: {'相同' if is_same else '不同'} (距离: {distance:.3f})")
            
            return result
            
        except Exception as e:
            self.logger.error(f"人脸比较失败: {str(e)}")
            return {
                'is_same_person': False,
                'confidence': 0.0,
                'distance': float('inf'),
                'processing_time': time.time() - start_time,
                'error': str(e)
            }
    
    def get_database_info(self) -> Dict:
        """
        获取人脸数据库信息
        
        Returns:
            数据库信息
        """
        return {
            'total_faces': len(self.known_face_names),
            'names': self.known_face_names.copy(),
            'tolerance': self.tolerance,
            'model': self.model,
            'database_path': self.database_path
        }
    
    def remove_face(self, name: str) -> bool:
        """
        从数据库中移除指定人脸
        
        Args:
            name: 要移除的人脸姓名
            
        Returns:
            是否移除成功
        """
        try:
            if name in self.known_face_names:
                index = self.known_face_names.index(name)
                
                # 移除对应的数据
                self.known_face_encodings.pop(index)
                self.known_face_names.pop(index)
                self.known_face_metadata.pop(index)
                
                # 保存数据库
                self._save_face_database()
                
                self.logger.info(f"成功移除人脸: {name}")
                return True
            else:
                self.logger.warning(f"未找到要移除的人脸: {name}")
                return False
                
        except Exception as e:
            self.logger.error(f"移除人脸失败: {str(e)}")
            return False
    
    def clear_database(self) -> bool:
        """
        清空人脸数据库
        
        Returns:
            是否清空成功
        """
        try:
            self.known_face_encodings = []
            self.known_face_names = []
            self.known_face_metadata = []
            
            # 保存空数据库
            self._save_face_database()
            
            self.logger.info("成功清空人脸数据库")
            return True
            
        except Exception as e:
            self.logger.error(f"清空人脸数据库失败: {str(e)}")
            return False
