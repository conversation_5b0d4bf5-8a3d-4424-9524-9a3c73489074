#!/usr/bin/env python3
"""
系统健康检查脚本
System Health Check Script
"""

import requests
import json
import time
from datetime import datetime

def check_backend_health():
    """检查后端健康状态"""
    print("🔍 检查后端服务状态...")
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 后端服务正常: {data.get('message', 'OK')}")
            return True
        else:
            print(f"❌ 后端服务异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端服务连接失败: {e}")
        return False

def check_frontend_access():
    """检查前端访问"""
    print("\n🌐 检查前端服务状态...")
    ports = [3000, 3001]
    
    for port in ports:
        try:
            response = requests.get(f"http://localhost:{port}", timeout=5)
            if response.status_code == 200:
                print(f"✅ 前端服务正常运行在端口 {port}")
                return port
        except:
            continue
    
    print("❌ 前端服务无法访问")
    return None

def check_api_endpoints():
    """检查API端点"""
    print("\n📡 检查API端点...")
    
    endpoints = [
        ("系统信息", "GET", "/info"),
        ("基础分析状态", "GET", "/api/v1/batch/status"),
        ("实时分析状态", "GET", "/api/v1/realtime/status"),
        ("数据库信息", "GET", "/api/v1/database-info"),
        ("每日统计", "GET", "/api/v1/analytics/daily-stats?days=7"),
        ("表情统计", "GET", "/api/v1/analytics/emotion-stats?days=30"),
        ("综合报告", "GET", "/api/v1/analytics/comprehensive-report?days=30")
    ]
    
    results = []
    base_url = "http://localhost:8000"
    
    for name, method, endpoint in endpoints:
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{endpoint}", timeout=10)
            else:
                continue
                
            if response.status_code == 200:
                print(f"✅ {name}: 正常")
                results.append(True)
            else:
                print(f"⚠️  {name}: HTTP {response.status_code}")
                results.append(False)
        except Exception as e:
            print(f"❌ {name}: {e}")
            results.append(False)
    
    return results

def check_advanced_features():
    """检查高级功能可用性"""
    print("\n🚀 检查高级功能...")
    
    advanced_endpoints = [
        "综合分析端点",
        "美颜滤镜端点", 
        "人脸比对端点",
        "数据分析端点"
    ]
    
    # 这里只检查端点是否响应，不进行实际的图像处理
    try:
        # 检查高级功能路由是否注册
        response = requests.get("http://localhost:8000/docs", timeout=5)
        if response.status_code == 200:
            print("✅ API文档可访问，高级功能路由已注册")
            return True
        else:
            print("⚠️  API文档访问异常")
            return False
    except Exception as e:
        print(f"❌ 高级功能检查失败: {e}")
        return False

def check_database_connectivity():
    """检查数据库连接"""
    print("\n💾 检查数据库连接...")
    
    try:
        # 通过API检查数据库状态
        response = requests.get("http://localhost:8000/api/v1/database-info", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                db_info = data.get('database_info', {})
                print(f"✅ 人脸数据库连接正常")
                print(f"   - 总人脸数: {db_info.get('total_faces', 0)}")
                print(f"   - 识别模型: {db_info.get('model', 'unknown')}")
                return True
            else:
                print("⚠️  数据库响应异常")
                return False
        else:
            print(f"❌ 数据库连接失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def check_ai_services():
    """检查AI服务状态"""
    print("\n🤖 检查AI服务状态...")
    
    try:
        response = requests.get("http://localhost:8000/info", timeout=5)
        if response.status_code == 200:
            data = response.json()
            features = data.get('features', [])
            
            expected_features = [
                '人脸检测',
                '表情分析', 
                '年龄性别预测',
                '人脸识别',
                '实时分析',
                '批量处理'
            ]
            
            available_count = 0
            for feature in expected_features:
                if any(feature in f for f in features):
                    print(f"✅ {feature}: 可用")
                    available_count += 1
                else:
                    print(f"⚠️  {feature}: 状态未知")
            
            print(f"✅ AI服务状态: {available_count}/{len(expected_features)} 个功能可用")
            return available_count >= len(expected_features) * 0.8  # 80%以上功能可用
            
        else:
            print(f"❌ AI服务检查失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ AI服务检查失败: {e}")
        return False

def generate_health_report():
    """生成健康检查报告"""
    print("\n📋 生成系统健康报告...")
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "system_status": "healthy",
        "checks_performed": [],
        "recommendations": []
    }
    
    # 执行所有检查
    checks = [
        ("后端服务", check_backend_health),
        ("前端服务", check_frontend_access),
        ("API端点", lambda: all(check_api_endpoints())),
        ("高级功能", check_advanced_features),
        ("数据库连接", check_database_connectivity),
        ("AI服务", check_ai_services)
    ]
    
    passed_checks = 0
    total_checks = len(checks)
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            status = "PASS" if result else "FAIL"
            report["checks_performed"].append({
                "name": check_name,
                "status": status,
                "timestamp": datetime.now().isoformat()
            })
            if result:
                passed_checks += 1
        except Exception as e:
            report["checks_performed"].append({
                "name": check_name,
                "status": "ERROR",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
    
    # 确定整体系统状态
    health_percentage = (passed_checks / total_checks) * 100
    
    if health_percentage >= 90:
        report["system_status"] = "excellent"
        report["recommendations"].append("系统运行状态优秀，所有功能正常")
    elif health_percentage >= 70:
        report["system_status"] = "good"
        report["recommendations"].append("系统运行状态良好，部分功能可能需要关注")
    elif health_percentage >= 50:
        report["system_status"] = "fair"
        report["recommendations"].append("系统运行状态一般，建议检查失败的功能")
    else:
        report["system_status"] = "poor"
        report["recommendations"].append("系统运行状态较差，需要立即检查和修复")
    
    return report, health_percentage

def main():
    """主函数"""
    print("🏥 人脸分析系统健康检查")
    print("=" * 50)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # 生成健康报告
    report, health_percentage = generate_health_report()
    
    # 输出结果
    print("\n" + "=" * 50)
    print("📊 系统健康检查结果")
    print("=" * 50)
    
    print(f"整体健康度: {health_percentage:.1f}%")
    print(f"系统状态: {report['system_status'].upper()}")
    
    print("\n详细检查结果:")
    for check in report["checks_performed"]:
        status_icon = "✅" if check["status"] == "PASS" else "❌" if check["status"] == "FAIL" else "⚠️"
        print(f"{status_icon} {check['name']}: {check['status']}")
    
    print("\n建议:")
    for recommendation in report["recommendations"]:
        print(f"💡 {recommendation}")
    
    # 保存报告
    report_filename = f"health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    try:
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"\n📄 详细报告已保存到: {report_filename}")
    except Exception as e:
        print(f"\n⚠️  报告保存失败: {e}")
    
    # 访问信息
    print("\n🌐 系统访问信息:")
    print("   前端界面: http://localhost:3001 (或 http://localhost:3000)")
    print("   后端API: http://localhost:8000")
    print("   API文档: http://localhost:8000/docs")
    
    print("\n🎯 主要功能页面:")
    print("   • 首页: http://localhost:3001/")
    print("   • 图片分析: http://localhost:3001/analysis")
    print("   • 实时分析: http://localhost:3001/realtime")
    print("   • 批量处理: http://localhost:3001/batch")
    print("   • 高级功能: http://localhost:3001/advanced")
    print("   • 数据分析: http://localhost:3001/analytics")
    print("   • 人脸数据库: http://localhost:3001/database")
    
    return 0 if health_percentage >= 70 else 1

if __name__ == "__main__":
    exit(main())
