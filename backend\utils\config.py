"""
配置文件
Configuration Settings
"""

import os
from pathlib import Path
from pydantic import BaseSettings
from typing import List

class Settings(BaseSettings):
    """应用配置"""
    
    # 基本配置
    APP_NAME: str = "人脸面部分析系统"
    VERSION: str = "1.0.0"
    DEBUG: bool = True
    
    # 服务器配置
    HOST: str = "127.0.0.1"
    PORT: int = 8000
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./facial_analysis.db"
    
    # 文件上传配置
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: List[str] = [".jpg", ".jpeg", ".png", ".bmp", ".tiff"]
    
    # AI模型配置
    MODELS_DIR: str = "models"
    
    # 人脸检测配置
    FACE_DETECTION_CONFIDENCE: float = 0.5
    FACE_DETECTION_MODEL: str = "mediapipe"  # mediapipe, opencv, dlib
    
    # 表情分析配置
    EMOTION_MODEL_PATH: str = "models/emotion_model.h5"
    EMOTION_LABELS: List[str] = [
        "angry", "disgust", "fear", "happy", 
        "neutral", "sad", "surprise"
    ]
    EMOTION_LABELS_CN: List[str] = [
        "愤怒", "厌恶", "恐惧", "快乐",
        "中性", "悲伤", "惊讶"
    ]
    
    # 年龄性别预测配置
    AGE_MODEL_PATH: str = "models/age_model.h5"
    GENDER_MODEL_PATH: str = "models/gender_model.h5"
    AGE_RANGES: List[str] = [
        "0-10", "11-20", "21-30", "31-40", 
        "41-50", "51-60", "61-70", "71+"
    ]
    
    # 人脸识别配置
    FACE_RECOGNITION_TOLERANCE: float = 0.6
    FACE_ENCODING_MODEL: str = "large"  # small, large
    
    # 实时分析配置
    REALTIME_FPS: int = 30
    REALTIME_RESOLUTION: tuple = (640, 480)
    
    # 批量处理配置
    BATCH_SIZE: int = 10
    MAX_BATCH_FILES: int = 100
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/facial_analysis.log"
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-here"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:5173",
        "http://127.0.0.1:5173"
    ]
    
    # 缓存配置
    CACHE_TTL: int = 3600  # 1小时
    ENABLE_CACHE: bool = True
    
    # 性能配置
    MAX_WORKERS: int = 4
    ENABLE_GPU: bool = False
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# 创建配置实例
settings = Settings()

# 确保必要的目录存在
def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        settings.UPLOAD_DIR,
        settings.MODELS_DIR,
        "logs",
        "cache",
        "temp"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

# 初始化目录
ensure_directories()

# 模型文件路径配置
MODEL_PATHS = {
    "face_detection": {
        "mediapipe": None,  # MediaPipe内置模型
        "opencv": "models/opencv_face_detector.pbtxt",
        "dlib": "models/shape_predictor_68_face_landmarks.dat"
    },
    "emotion": settings.EMOTION_MODEL_PATH,
    "age": settings.AGE_MODEL_PATH,
    "gender": settings.GENDER_MODEL_PATH,
    "landmarks": "models/shape_predictor_68_face_landmarks.dat"
}

# 支持的图像格式
SUPPORTED_IMAGE_FORMATS = {
    ".jpg": "JPEG",
    ".jpeg": "JPEG", 
    ".png": "PNG",
    ".bmp": "BMP",
    ".tiff": "TIFF",
    ".tif": "TIFF"
}

# 分析结果配置
ANALYSIS_CONFIG = {
    "include_landmarks": True,
    "include_emotions": True,
    "include_age_gender": True,
    "include_face_encoding": True,
    "return_cropped_faces": True,
    "return_annotated_image": True
}

# 实时分析配置
REALTIME_CONFIG = {
    "enable_face_detection": True,
    "enable_emotion_analysis": True,
    "enable_age_gender": True,
    "enable_landmarks": True,
    "show_confidence": True,
    "show_fps": True
}

# API响应配置
API_RESPONSE_CONFIG = {
    "include_processing_time": True,
    "include_model_info": True,
    "include_system_info": False,
    "compress_large_responses": True
}
