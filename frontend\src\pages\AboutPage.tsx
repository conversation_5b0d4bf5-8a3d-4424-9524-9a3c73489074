import React from 'react'
import { motion } from 'framer-motion'
import { 
  Brain, 
  Zap, 
  Shield, 
  Smartphone, 
  Github, 
  Mail, 
  Globe,
  Award,
  Users,
  Clock,
  Star
} from 'lucide-react'

const AboutPage = () => {
  const features = [
    {
      icon: Brain,
      title: '先进AI技术',
      description: '基于最新的深度学习算法，提供高精度的人脸分析能力'
    },
    {
      icon: Zap,
      title: '高性能处理',
      description: '优化的模型推理速度，毫秒级响应，支持实时分析'
    },
    {
      icon: Shield,
      title: '隐私保护',
      description: '本地处理，数据不上传，确保用户隐私安全'
    },
    {
      icon: Smartphone,
      title: '跨平台支持',
      description: '支持多种设备和浏览器，响应式设计适配各种屏幕'
    }
  ]

  const stats = [
    { icon: Award, label: '检测准确率', value: '99.5%' },
    { icon: Clock, label: '平均响应时间', value: '<100ms' },
    { icon: Users, label: '支持同时检测', value: '10+人脸' },
    { icon: Star, label: '用户满意度', value: '4.9/5' }
  ]

  const technologies = [
    'Python', 'FastAPI', 'React', 'TypeScript', 'TailwindCSS',
    'OpenCV', 'MediaPipe', 'TensorFlow', 'Face Recognition', 'WebRTC'
  ]

  return (
    <div className="max-w-6xl mx-auto space-y-16">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h1 className="text-4xl font-bold text-slate-900 mb-6">
          关于我们
        </h1>
        <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
          我们致力于开发最先进、最易用的人脸分析技术，为用户提供专业级的AI分析服务。
          通过不断的技术创新和用户体验优化，打造业界领先的人脸分析解决方案。
        </p>
      </motion.div>

      {/* 项目介绍 */}
      <motion.section
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="card p-8"
      >
        <h2 className="text-2xl font-bold text-slate-900 mb-6 text-center">
          项目简介
        </h2>
        <div className="prose prose-lg max-w-none text-slate-600">
          <p>
            人脸面部分析系统是一个基于现代AI技术的综合性人脸分析平台。系统集成了人脸检测、
            表情识别、年龄性别预测、身份验证等多项核心功能，为用户提供一站式的人脸分析服务。
          </p>
          <p>
            我们采用了业界最先进的深度学习算法和计算机视觉技术，确保分析结果的准确性和可靠性。
            同时，系统设计注重用户体验，提供直观易用的操作界面和丰富的功能选项。
          </p>
          <p>
            无论是个人用户的日常使用，还是企业级的批量处理需求，我们的系统都能提供专业、
            高效的解决方案。
          </p>
        </div>
      </motion.section>

      {/* 核心特性 */}
      <motion.section
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <h2 className="text-2xl font-bold text-slate-900 mb-8 text-center">
          核心特性
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {features.map((feature, index) => {
            const Icon = feature.icon
            return (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * index }}
                className="card p-6"
              >
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                    <Icon className="w-6 h-6 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-slate-900 mb-2">
                      {feature.title}
                    </h3>
                    <p className="text-slate-600">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            )
          })}
        </div>
      </motion.section>

      {/* 技术统计 */}
      <motion.section
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <h2 className="text-2xl font-bold text-slate-900 mb-8 text-center">
          技术指标
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.1 * index }}
                className="card text-center p-6"
              >
                <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-100 text-primary-600 rounded-lg mb-4">
                  <Icon className="w-6 h-6" />
                </div>
                <div className="text-2xl font-bold text-slate-900 mb-1">
                  {stat.value}
                </div>
                <div className="text-sm text-slate-600">
                  {stat.label}
                </div>
              </motion.div>
            )
          })}
        </div>
      </motion.section>

      {/* 技术栈 */}
      <motion.section
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="card p-8"
      >
        <h2 className="text-2xl font-bold text-slate-900 mb-6 text-center">
          技术栈
        </h2>
        <div className="flex flex-wrap justify-center gap-3">
          {technologies.map((tech, index) => (
            <motion.span
              key={tech}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.05 * index }}
              className="px-4 py-2 bg-slate-100 text-slate-700 rounded-full text-sm font-medium hover:bg-primary-100 hover:text-primary-700 transition-colors duration-200"
            >
              {tech}
            </motion.span>
          ))}
        </div>
      </motion.section>

      {/* 联系信息 */}
      <motion.section
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="card p-8 bg-gradient-to-br from-primary-50 to-blue-50 border-primary-200"
      >
        <h2 className="text-2xl font-bold text-slate-900 mb-6 text-center">
          联系我们
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
          <div className="space-y-2">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-100 text-primary-600 rounded-lg mx-auto">
              <Github className="w-6 h-6" />
            </div>
            <h3 className="font-semibold text-slate-900">GitHub</h3>
            <p className="text-slate-600 text-sm">
              查看源代码和贡献
            </p>
          </div>
          
          <div className="space-y-2">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-100 text-primary-600 rounded-lg mx-auto">
              <Mail className="w-6 h-6" />
            </div>
            <h3 className="font-semibold text-slate-900">邮箱</h3>
            <p className="text-slate-600 text-sm">
              技术支持和反馈
            </p>
          </div>
          
          <div className="space-y-2">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-100 text-primary-600 rounded-lg mx-auto">
              <Globe className="w-6 h-6" />
            </div>
            <h3 className="font-semibold text-slate-900">官网</h3>
            <p className="text-slate-600 text-sm">
              了解更多产品信息
            </p>
          </div>
        </div>
      </motion.section>

      {/* 版权信息 */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.7 }}
        className="text-center text-slate-500 text-sm"
      >
        <p>© 2024 人脸面部分析系统. 保留所有权利.</p>
        <p className="mt-1">基于开源技术构建，致力于推动AI技术的普及和应用。</p>
      </motion.div>
    </div>
  )
}

export default AboutPage
