#!/usr/bin/env python3
"""
全面功能测试脚本
Comprehensive Function Test Script
"""

import requests
import json
import time
import os
import cv2
import numpy as np
from datetime import datetime

def create_test_image():
    """创建测试图像"""
    try:
        # 创建一个包含人脸的测试图像
        img = np.zeros((400, 400, 3), dtype=np.uint8)
        img.fill(240)  # 浅灰色背景
        
        # 绘制简单的人脸
        center = (200, 200)
        
        # 面部轮廓
        cv2.ellipse(img, center, (120, 150), 0, 0, 360, (220, 200, 180), -1)
        
        # 眼睛
        cv2.ellipse(img, (170, 170), (25, 15), 0, 0, 360, (255, 255, 255), -1)
        cv2.ellipse(img, (230, 170), (25, 15), 0, 0, 360, (255, 255, 255), -1)
        cv2.circle(img, (170, 170), 8, (0, 0, 0), -1)
        cv2.circle(img, (230, 170), 8, (0, 0, 0), -1)
        
        # 鼻子
        cv2.ellipse(img, (200, 200), (8, 20), 0, 0, 360, (200, 180, 160), -1)
        
        # 嘴巴
        cv2.ellipse(img, (200, 240), (40, 20), 0, 0, 180, (180, 100, 100), -1)
        
        test_image_path = "test_face.jpg"
        cv2.imwrite(test_image_path, img)
        print(f"✅ 创建测试图片: {test_image_path}")
        return test_image_path
    except Exception as e:
        print(f"❌ 创建测试图片失败: {e}")
        return None

def test_basic_endpoints():
    """测试基础端点"""
    print("\n🔍 测试基础端点...")
    
    tests = []
    base_url = "http://localhost:8000"
    
    # 系统信息端点
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        tests.append(("系统健康检查", response.status_code == 200))
    except:
        tests.append(("系统健康检查", False))
    
    try:
        response = requests.get(f"{base_url}/info", timeout=5)
        tests.append(("系统信息", response.status_code == 200))
    except:
        tests.append(("系统信息", False))
    
    # API文档
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        tests.append(("API文档", response.status_code == 200))
    except:
        tests.append(("API文档", False))
    
    return tests

def test_face_analysis_api(image_path):
    """测试人脸分析API"""
    print("\n👤 测试人脸分析API...")
    
    tests = []
    base_url = "http://localhost:8000"
    
    if not image_path or not os.path.exists(image_path):
        return [("人脸分析API", False)]
    
    # 基础人脸分析
    try:
        with open(image_path, 'rb') as f:
            files = {'file': ('test_face.jpg', f, 'image/jpeg')}
            response = requests.post(f"{base_url}/api/v1/analyze", files=files, timeout=30)
        tests.append(("基础人脸分析", response.status_code == 200))
    except Exception as e:
        tests.append(("基础人脸分析", False))
        print(f"   错误: {e}")
    
    # 批量处理状态
    try:
        response = requests.get(f"{base_url}/api/v1/batch/status", timeout=5)
        tests.append(("批量处理状态", response.status_code == 200))
    except:
        tests.append(("批量处理状态", False))
    
    # 实时分析状态
    try:
        response = requests.get(f"{base_url}/api/v1/realtime/status", timeout=5)
        tests.append(("实时分析状态", response.status_code == 200))
    except:
        tests.append(("实时分析状态", False))
    
    return tests

def test_advanced_features_api(image_path):
    """测试高级功能API"""
    print("\n🚀 测试高级功能API...")
    
    tests = []
    base_url = "http://localhost:8000"
    
    if not image_path or not os.path.exists(image_path):
        return [("高级功能API", False)]
    
    # 综合分析
    try:
        with open(image_path, 'rb') as f:
            files = {'file': ('test_face.jpg', f, 'image/jpeg')}
            response = requests.post(f"{base_url}/api/v1/advanced/comprehensive-analysis", files=files, timeout=30)
        tests.append(("综合分析", response.status_code == 200))
        if response.status_code == 200:
            data = response.json()
            tests.append(("综合分析-数据格式", data.get('success') is not None))
    except Exception as e:
        tests.append(("综合分析", False))
        print(f"   错误: {e}")
    
    # 美颜滤镜
    try:
        with open(image_path, 'rb') as f:
            files = {'file': ('test_face.jpg', f, 'image/jpeg')}
            data = {'settings': json.dumps({'skin_smoothing': 0.5, 'brightness': 10})}
            response = requests.post(f"{base_url}/api/v1/advanced/beauty-filter", files=files, data=data, timeout=30)
        tests.append(("美颜滤镜", response.status_code == 200))
    except Exception as e:
        tests.append(("美颜滤镜", False))
        print(f"   错误: {e}")
    
    return tests

def test_super_advanced_api(image_path):
    """测试超级高级功能API"""
    print("\n⚡ 测试超级高级功能API...")
    
    tests = []
    base_url = "http://localhost:8000"
    
    if not image_path or not os.path.exists(image_path):
        return [("超级高级功能API", False)]
    
    # 3D分析
    try:
        with open(image_path, 'rb') as f:
            files = {'file': ('test_face.jpg', f, 'image/jpeg')}
            response = requests.post(f"{base_url}/api/v1/advanced/3d-analysis", files=files, timeout=30)
        tests.append(("3D分析", response.status_code == 200))
        if response.status_code == 200:
            data = response.json()
            tests.append(("3D分析-数据格式", data.get('success') is not None))
    except Exception as e:
        tests.append(("3D分析", False))
        print(f"   错误: {e}")
    
    # 情感AI
    try:
        with open(image_path, 'rb') as f:
            files = {'file': ('test_face.jpg', f, 'image/jpeg')}
            response = requests.post(f"{base_url}/api/v1/advanced/emotion-ai", files=files, timeout=30)
        tests.append(("情感AI", response.status_code == 200))
    except Exception as e:
        tests.append(("情感AI", False))
        print(f"   错误: {e}")
    
    # 智能推荐
    try:
        with open(image_path, 'rb') as f:
            files = {'file': ('test_face.jpg', f, 'image/jpeg')}
            response = requests.post(f"{base_url}/api/v1/advanced/smart-recommendations", files=files, timeout=30)
        tests.append(("智能推荐", response.status_code == 200))
    except Exception as e:
        tests.append(("智能推荐", False))
        print(f"   错误: {e}")
    
    return tests

def test_analytics_api():
    """测试数据分析API"""
    print("\n📊 测试数据分析API...")
    
    tests = []
    base_url = "http://localhost:8000"
    
    # 每日统计
    try:
        response = requests.get(f"{base_url}/api/v1/analytics/daily-stats?days=7", timeout=10)
        tests.append(("每日统计", response.status_code == 200))
    except:
        tests.append(("每日统计", False))
    
    # 表情统计
    try:
        response = requests.get(f"{base_url}/api/v1/analytics/emotion-stats?days=30", timeout=10)
        tests.append(("表情统计", response.status_code == 200))
    except:
        tests.append(("表情统计", False))
    
    # 人口统计
    try:
        response = requests.get(f"{base_url}/api/v1/analytics/demographic-stats?days=30", timeout=10)
        tests.append(("人口统计", response.status_code == 200))
    except:
        tests.append(("人口统计", False))
    
    # 性能统计
    try:
        response = requests.get(f"{base_url}/api/v1/analytics/performance-stats?days=7", timeout=10)
        tests.append(("性能统计", response.status_code == 200))
    except:
        tests.append(("性能统计", False))
    
    # 综合报告
    try:
        response = requests.get(f"{base_url}/api/v1/analytics/comprehensive-report?days=30", timeout=10)
        tests.append(("综合报告", response.status_code == 200))
    except:
        tests.append(("综合报告", False))
    
    return tests

def test_frontend_pages():
    """测试前端页面"""
    print("\n🌐 测试前端页面...")
    
    tests = []
    base_url = "http://localhost:3000"
    
    pages = [
        ("首页", "/"),
        ("图片分析", "/analysis"),
        ("实时分析", "/realtime"),
        ("批量处理", "/batch"),
        ("高级功能", "/advanced"),
        ("超级功能", "/super-advanced"),
        ("数据分析", "/analytics"),
        ("人脸数据库", "/database"),
        ("关于页面", "/about")
    ]
    
    for name, path in pages:
        try:
            response = requests.get(f"{base_url}{path}", timeout=10)
            tests.append((name, response.status_code == 200))
        except:
            tests.append((name, False))
    
    return tests

def run_comprehensive_test():
    """运行全面测试"""
    print("🧪 开始全面功能测试")
    print("=" * 60)
    
    # 创建测试图像
    test_image = create_test_image()
    
    all_tests = []
    
    # 运行各项测试
    all_tests.extend(test_basic_endpoints())
    all_tests.extend(test_face_analysis_api(test_image))
    all_tests.extend(test_advanced_features_api(test_image))
    all_tests.extend(test_super_advanced_api(test_image))
    all_tests.extend(test_analytics_api())
    all_tests.extend(test_frontend_pages())
    
    # 清理测试文件
    if test_image and os.path.exists(test_image):
        os.remove(test_image)
        print(f"🧹 清理测试文件: {test_image}")
    
    # 统计结果
    passed = sum(1 for _, result in all_tests if result)
    total = len(all_tests)
    
    print(f"\n📊 测试结果汇总")
    print("=" * 60)
    
    # 按类别显示结果
    categories = {
        "基础功能": [],
        "人脸分析": [],
        "高级功能": [],
        "超级功能": [],
        "数据分析": [],
        "前端页面": []
    }
    
    for name, result in all_tests:
        status = "✅ 通过" if result else "❌ 失败"
        
        if any(x in name for x in ["系统", "API文档"]):
            categories["基础功能"].append(f"  {name}: {status}")
        elif any(x in name for x in ["人脸分析", "批量", "实时"]):
            categories["人脸分析"].append(f"  {name}: {status}")
        elif any(x in name for x in ["综合分析", "美颜"]):
            categories["高级功能"].append(f"  {name}: {status}")
        elif any(x in name for x in ["3D", "情感", "推荐"]):
            categories["超级功能"].append(f"  {name}: {status}")
        elif any(x in name for x in ["统计", "报告"]):
            categories["数据分析"].append(f"  {name}: {status}")
        else:
            categories["前端页面"].append(f"  {name}: {status}")
    
    for category, tests in categories.items():
        if tests:
            print(f"\n{category}:")
            for test in tests:
                print(test)
    
    print(f"\n总体结果: {passed}/{total} 项测试通过 ({passed/total*100:.1f}%)")
    
    if passed >= total * 0.9:
        print("🎉 系统功能优秀！所有主要功能正常运行")
        return True
    elif passed >= total * 0.7:
        print("✅ 系统功能良好！大部分功能正常运行")
        return True
    else:
        print("⚠️  系统存在问题，需要检查失败的功能")
        return False

def main():
    """主函数"""
    print(f"🧪 全面功能测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = run_comprehensive_test()
    
    print(f"\n🎯 测试建议:")
    if success:
        print("  ✨ 系统运行状态优秀，可以正常使用所有功能")
        print("  🚀 建议体验以下核心功能:")
        print("     • 图片分析 - 上传图片进行基础分析")
        print("     • 高级功能 - 体验美颜滤镜和综合分析")
        print("     • 超级功能 - 尝试3D重建和情感AI")
        print("     • 数据分析 - 查看使用统计和可视化")
    else:
        print("  ⚠️  建议检查失败的功能模块")
        print("  🔧 可能需要重启服务或检查依赖")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
