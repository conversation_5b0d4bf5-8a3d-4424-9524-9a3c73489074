"""
表情分析服务
Emotion Analysis Service
"""

import cv2
import numpy as np
import tensorflow as tf
from typing import List, Dict, Tuple
import time
import os

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from utils.logger import app_logger
from utils.config import settings

class EmotionAnalyzer:
    """表情分析器"""
    
    def __init__(self):
        """初始化表情分析器"""
        self.logger = app_logger
        self.emotion_labels = settings.EMOTION_LABELS
        self.emotion_labels_cn = settings.EMOTION_LABELS_CN
        self.model = None
        
        # 加载预训练模型
        self._load_model()
        
        self.logger.info("表情分析器初始化完成")
    
    def _load_model(self):
        """加载表情识别模型"""
        try:
            model_path = settings.EMOTION_MODEL_PATH
            
            if os.path.exists(model_path):
                self.model = tf.keras.models.load_model(model_path)
                self.logger.info(f"成功加载表情模型: {model_path}")
            else:
                # 如果模型文件不存在，创建一个简单的CNN模型
                self.model = self._create_default_model()
                self.logger.warning(f"模型文件不存在，使用默认模型: {model_path}")
                
        except Exception as e:
            self.logger.error(f"加载表情模型失败: {str(e)}")
            self.model = self._create_default_model()
    
    def _create_default_model(self):
        """创建默认的表情识别模型"""
        try:
            model = tf.keras.Sequential([
                tf.keras.layers.Conv2D(32, (3, 3), activation='relu', input_shape=(48, 48, 1)),
                tf.keras.layers.MaxPooling2D((2, 2)),
                tf.keras.layers.Conv2D(64, (3, 3), activation='relu'),
                tf.keras.layers.MaxPooling2D((2, 2)),
                tf.keras.layers.Conv2D(128, (3, 3), activation='relu'),
                tf.keras.layers.MaxPooling2D((2, 2)),
                tf.keras.layers.Flatten(),
                tf.keras.layers.Dense(128, activation='relu'),
                tf.keras.layers.Dropout(0.5),
                tf.keras.layers.Dense(len(self.emotion_labels), activation='softmax')
            ])
            
            model.compile(
                optimizer='adam',
                loss='categorical_crossentropy',
                metrics=['accuracy']
            )
            
            self.logger.info("创建默认表情识别模型")
            return model
            
        except Exception as e:
            self.logger.error(f"创建默认模型失败: {str(e)}")
            return None
    
    def preprocess_face(self, face_image: np.ndarray) -> np.ndarray:
        """
        预处理人脸图像用于表情识别
        
        Args:
            face_image: 人脸图像
            
        Returns:
            预处理后的图像
        """
        try:
            # 转换为灰度图
            if len(face_image.shape) == 3:
                gray = cv2.cvtColor(face_image, cv2.COLOR_BGR2GRAY)
            else:
                gray = face_image
            
            # 调整大小到48x48
            resized = cv2.resize(gray, (48, 48))
            
            # 归一化
            normalized = resized.astype('float32') / 255.0
            
            # 添加批次维度和通道维度
            processed = np.expand_dims(normalized, axis=0)
            processed = np.expand_dims(processed, axis=-1)
            
            return processed
            
        except Exception as e:
            self.logger.error(f"人脸预处理失败: {str(e)}")
            return None
    
    def analyze_emotion(self, face_image: np.ndarray) -> Dict:
        """
        分析单张人脸的表情
        
        Args:
            face_image: 人脸图像
            
        Returns:
            表情分析结果
        """
        start_time = time.time()
        
        try:
            if self.model is None:
                return {
                    'emotion': 'unknown',
                    'emotion_cn': '未知',
                    'confidence': 0.0,
                    'probabilities': {},
                    'processing_time': 0.0
                }
            
            # 预处理图像
            processed_image = self.preprocess_face(face_image)
            if processed_image is None:
                raise ValueError("图像预处理失败")
            
            # 进行预测
            predictions = self.model.predict(processed_image, verbose=0)
            probabilities = predictions[0]
            
            # 获取最高概率的表情
            max_index = np.argmax(probabilities)
            emotion = self.emotion_labels[max_index]
            emotion_cn = self.emotion_labels_cn[max_index]
            confidence = float(probabilities[max_index])
            
            # 构建概率字典
            prob_dict = {}
            prob_dict_cn = {}
            for i, (label, label_cn) in enumerate(zip(self.emotion_labels, self.emotion_labels_cn)):
                prob_dict[label] = float(probabilities[i])
                prob_dict_cn[label_cn] = float(probabilities[i])
            
            processing_time = time.time() - start_time
            
            result = {
                'emotion': emotion,
                'emotion_cn': emotion_cn,
                'confidence': confidence,
                'probabilities': prob_dict,
                'probabilities_cn': prob_dict_cn,
                'processing_time': processing_time
            }
            
            self.logger.debug(f"表情分析完成: {emotion_cn} ({confidence:.3f}), 耗时 {processing_time:.3f}s")
            
            return result
            
        except Exception as e:
            self.logger.error(f"表情分析失败: {str(e)}")
            return {
                'emotion': 'error',
                'emotion_cn': '错误',
                'confidence': 0.0,
                'probabilities': {},
                'processing_time': time.time() - start_time,
                'error': str(e)
            }
    
    def analyze_emotions_batch(self, face_images: List[np.ndarray]) -> List[Dict]:
        """
        批量分析多张人脸的表情
        
        Args:
            face_images: 人脸图像列表
            
        Returns:
            表情分析结果列表
        """
        start_time = time.time()
        results = []
        
        try:
            if self.model is None:
                return [self.analyze_emotion(face) for face in face_images]
            
            # 批量预处理
            processed_images = []
            valid_indices = []
            
            for i, face_image in enumerate(face_images):
                processed = self.preprocess_face(face_image)
                if processed is not None:
                    processed_images.append(processed[0])  # 移除批次维度
                    valid_indices.append(i)
            
            if not processed_images:
                return [self.analyze_emotion(face) for face in face_images]
            
            # 批量预测
            batch_input = np.array(processed_images)
            predictions = self.model.predict(batch_input, verbose=0)
            
            # 处理结果
            for i, face_image in enumerate(face_images):
                if i in valid_indices:
                    pred_index = valid_indices.index(i)
                    probabilities = predictions[pred_index]
                    
                    max_index = np.argmax(probabilities)
                    emotion = self.emotion_labels[max_index]
                    emotion_cn = self.emotion_labels_cn[max_index]
                    confidence = float(probabilities[max_index])
                    
                    prob_dict = {}
                    prob_dict_cn = {}
                    for j, (label, label_cn) in enumerate(zip(self.emotion_labels, self.emotion_labels_cn)):
                        prob_dict[label] = float(probabilities[j])
                        prob_dict_cn[label_cn] = float(probabilities[j])
                    
                    result = {
                        'emotion': emotion,
                        'emotion_cn': emotion_cn,
                        'confidence': confidence,
                        'probabilities': prob_dict,
                        'probabilities_cn': prob_dict_cn
                    }
                else:
                    result = self.analyze_emotion(face_image)
                
                results.append(result)
            
            processing_time = time.time() - start_time
            self.logger.info(f"批量表情分析完成: {len(face_images)} 张人脸，耗时 {processing_time:.3f}s")
            
            return results
            
        except Exception as e:
            self.logger.error(f"批量表情分析失败: {str(e)}")
            return [self.analyze_emotion(face) for face in face_images]
    
    def get_emotion_color(self, emotion: str) -> Tuple[int, int, int]:
        """
        获取表情对应的颜色（BGR格式）
        
        Args:
            emotion: 表情标签
            
        Returns:
            BGR颜色元组
        """
        color_map = {
            'happy': (0, 255, 0),      # 绿色
            'sad': (255, 0, 0),        # 蓝色
            'angry': (0, 0, 255),      # 红色
            'surprise': (0, 255, 255), # 黄色
            'fear': (128, 0, 128),     # 紫色
            'disgust': (0, 128, 128),  # 橄榄色
            'neutral': (128, 128, 128) # 灰色
        }
        
        return color_map.get(emotion, (255, 255, 255))  # 默认白色
    
    def draw_emotion_result(self, image: np.ndarray, bbox: Dict, 
                           emotion_result: Dict) -> np.ndarray:
        """
        在图像上绘制表情分析结果
        
        Args:
            image: 原始图像
            bbox: 人脸边界框
            emotion_result: 表情分析结果
            
        Returns:
            绘制了结果的图像
        """
        result_image = image.copy()
        
        try:
            emotion = emotion_result.get('emotion', 'unknown')
            emotion_cn = emotion_result.get('emotion_cn', '未知')
            confidence = emotion_result.get('confidence', 0.0)
            
            # 获取颜色
            color = self.get_emotion_color(emotion)
            
            # 绘制边界框
            cv2.rectangle(
                result_image,
                (bbox['x'], bbox['y']),
                (bbox['x'] + bbox['width'], bbox['y'] + bbox['height']),
                color, 2
            )
            
            # 绘制表情标签
            label = f"{emotion_cn}: {confidence:.2f}"
            cv2.putText(
                result_image,
                label,
                (bbox['x'], bbox['y'] - 10),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.6, color, 2
            )
            
        except Exception as e:
            self.logger.error(f"绘制表情结果失败: {str(e)}")
        
        return result_image
