#!/bin/bash

echo "启动人脸面部分析系统..."
echo

# 检查Python环境
echo "检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.8+"
    exit 1
fi
python3 --version

# 检查Node.js环境
echo "检查Node.js环境..."
if ! command -v node &> /dev/null; then
    echo "错误: 未找到Node.js，请先安装Node.js 16+"
    exit 1
fi
node --version

echo
echo "安装后端依赖..."
cd backend

# 创建虚拟环境
if [ ! -d "venv" ]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 安装Python依赖
echo "安装Python依赖..."
pip install -r requirements.txt

echo
echo "启动后端服务..."
gnome-terminal --title="后端服务" -- bash -c "source venv/bin/activate && python main.py; exec bash" &

cd ../frontend

echo
echo "安装前端依赖..."
if [ ! -d "node_modules" ]; then
    npm install
fi

echo
echo "启动前端服务..."
gnome-terminal --title="前端服务" -- bash -c "npm run dev; exec bash" &

echo
echo "系统启动完成！"
echo "后端服务: http://localhost:8000"
echo "前端服务: http://localhost:3000"
echo "API文档: http://localhost:8000/docs"
echo
echo "按Ctrl+C退出..."

# 等待用户中断
trap 'echo "正在关闭服务..."; exit 0' INT
while true; do
    sleep 1
done
