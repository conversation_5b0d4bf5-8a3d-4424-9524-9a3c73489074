"""
高级面部分析服务
Advanced Facial Analysis Service
"""

import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional
import time
import math

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from utils.logger import app_logger
from utils.config import settings

class AdvancedAnalysis:
    """高级面部分析器"""
    
    def __init__(self):
        """初始化高级分析器"""
        self.logger = app_logger
        self.logger.info("高级面部分析器初始化完成")
    
    def analyze_face_symmetry(self, landmarks: List[Dict]) -> Dict:
        """
        分析面部对称性
        
        Args:
            landmarks: 面部特征点
            
        Returns:
            对称性分析结果
        """
        try:
            if not landmarks:
                return {"symmetry_score": 0.0, "analysis": "无法分析"}
            
            points = landmarks[0]['landmarks']
            if len(points) < 68:
                return {"symmetry_score": 0.0, "analysis": "特征点不足"}
            
            # 计算面部中线
            nose_tip = points[30]  # 鼻尖
            chin = points[8]       # 下巴
            
            # 计算左右眼角距离中线的差异
            left_eye_corner = points[36]   # 左眼内角
            right_eye_corner = points[45]  # 右眼外角
            
            # 计算对称性得分 (简化算法)
            center_x = (left_eye_corner['x'] + right_eye_corner['x']) / 2
            nose_deviation = abs(nose_tip['x'] - center_x)
            
            # 归一化得分 (0-1)
            face_width = abs(right_eye_corner['x'] - left_eye_corner['x'])
            symmetry_score = max(0, 1 - (nose_deviation / face_width * 2))
            
            # 分析结果
            if symmetry_score > 0.9:
                analysis = "面部高度对称"
            elif symmetry_score > 0.7:
                analysis = "面部较为对称"
            elif symmetry_score > 0.5:
                analysis = "面部轻微不对称"
            else:
                analysis = "面部不对称明显"
            
            return {
                "symmetry_score": round(symmetry_score, 3),
                "analysis": analysis,
                "nose_deviation": round(nose_deviation, 2),
                "face_width": round(face_width, 2)
            }
            
        except Exception as e:
            self.logger.error(f"面部对称性分析失败: {str(e)}")
            return {"symmetry_score": 0.0, "analysis": "分析失败", "error": str(e)}
    
    def analyze_face_shape(self, landmarks: List[Dict]) -> Dict:
        """
        分析面部形状
        
        Args:
            landmarks: 面部特征点
            
        Returns:
            面部形状分析结果
        """
        try:
            if not landmarks:
                return {"face_shape": "unknown", "confidence": 0.0}
            
            points = landmarks[0]['landmarks']
            if len(points) < 17:  # 至少需要面部轮廓点
                return {"face_shape": "unknown", "confidence": 0.0}
            
            # 提取关键测量点
            forehead_width = abs(points[16]['x'] - points[0]['x'])  # 额头宽度
            cheek_width = abs(points[14]['x'] - points[2]['x'])     # 脸颊宽度
            jaw_width = abs(points[12]['x'] - points[4]['x'])       # 下颌宽度
            
            face_height = abs(points[8]['y'] - points[27]['y'])     # 面部高度
            
            # 计算比例
            width_height_ratio = max(forehead_width, cheek_width) / face_height
            jaw_forehead_ratio = jaw_width / forehead_width
            
            # 判断面部形状
            if width_height_ratio > 1.2:
                if jaw_forehead_ratio > 0.9:
                    face_shape = "圆形"
                    confidence = 0.8
                else:
                    face_shape = "方形"
                    confidence = 0.7
            elif width_height_ratio < 0.8:
                face_shape = "长形"
                confidence = 0.8
            else:
                if jaw_forehead_ratio < 0.7:
                    face_shape = "心形"
                    confidence = 0.7
                elif jaw_forehead_ratio > 1.1:
                    face_shape = "梨形"
                    confidence = 0.6
                else:
                    face_shape = "椭圆形"
                    confidence = 0.9
            
            return {
                "face_shape": face_shape,
                "confidence": confidence,
                "measurements": {
                    "forehead_width": round(forehead_width, 2),
                    "cheek_width": round(cheek_width, 2),
                    "jaw_width": round(jaw_width, 2),
                    "face_height": round(face_height, 2),
                    "width_height_ratio": round(width_height_ratio, 3),
                    "jaw_forehead_ratio": round(jaw_forehead_ratio, 3)
                }
            }
            
        except Exception as e:
            self.logger.error(f"面部形状分析失败: {str(e)}")
            return {"face_shape": "unknown", "confidence": 0.0, "error": str(e)}
    
    def analyze_skin_tone(self, face_image: np.ndarray) -> Dict:
        """
        分析肤色
        
        Args:
            face_image: 人脸图像
            
        Returns:
            肤色分析结果
        """
        try:
            # 转换到HSV色彩空间
            hsv = cv2.cvtColor(face_image, cv2.COLOR_BGR2HSV)
            
            # 提取面部中心区域 (避免头发和背景)
            h, w = face_image.shape[:2]
            center_region = face_image[h//4:3*h//4, w//4:3*w//4]
            
            # 计算平均颜色
            mean_color = np.mean(center_region.reshape(-1, 3), axis=0)
            b, g, r = mean_color
            
            # 转换为RGB
            rgb_color = (int(r), int(g), int(b))
            
            # 简单的肤色分类
            brightness = (r + g + b) / 3
            
            if brightness > 200:
                skin_tone = "白皙"
                tone_category = "fair"
            elif brightness > 160:
                skin_tone = "偏白"
                tone_category = "light"
            elif brightness > 120:
                skin_tone = "中等"
                tone_category = "medium"
            elif brightness > 80:
                skin_tone = "偏深"
                tone_category = "dark"
            else:
                skin_tone = "深色"
                tone_category = "very_dark"
            
            return {
                "skin_tone": skin_tone,
                "tone_category": tone_category,
                "rgb_color": rgb_color,
                "brightness": round(brightness, 2),
                "hex_color": f"#{int(r):02x}{int(g):02x}{int(b):02x}"
            }
            
        except Exception as e:
            self.logger.error(f"肤色分析失败: {str(e)}")
            return {"skin_tone": "unknown", "error": str(e)}
    
    def analyze_facial_features(self, landmarks: List[Dict]) -> Dict:
        """
        分析面部特征
        
        Args:
            landmarks: 面部特征点
            
        Returns:
            面部特征分析结果
        """
        try:
            if not landmarks:
                return {"features": {}, "analysis": "无法分析"}
            
            points = landmarks[0]['landmarks']
            if len(points) < 68:
                return {"features": {}, "analysis": "特征点不足"}
            
            features = {}
            
            # 眼睛分析
            left_eye_width = abs(points[39]['x'] - points[36]['x'])
            right_eye_width = abs(points[45]['x'] - points[42]['x'])
            eye_distance = abs(points[39]['x'] - points[42]['x'])
            
            avg_eye_width = (left_eye_width + right_eye_width) / 2
            eye_ratio = eye_distance / avg_eye_width
            
            if eye_ratio > 1.5:
                eye_analysis = "眼距较宽"
            elif eye_ratio < 1.0:
                eye_analysis = "眼距较窄"
            else:
                eye_analysis = "眼距适中"
            
            features["eyes"] = {
                "analysis": eye_analysis,
                "eye_distance": round(eye_distance, 2),
                "avg_eye_width": round(avg_eye_width, 2),
                "eye_ratio": round(eye_ratio, 3)
            }
            
            # 鼻子分析
            nose_width = abs(points[35]['x'] - points[31]['x'])
            nose_height = abs(points[33]['y'] - points[27]['y'])
            nose_ratio = nose_height / nose_width
            
            if nose_ratio > 1.5:
                nose_analysis = "鼻子较长"
            elif nose_ratio < 1.0:
                nose_analysis = "鼻子较短"
            else:
                nose_analysis = "鼻子比例适中"
            
            features["nose"] = {
                "analysis": nose_analysis,
                "nose_width": round(nose_width, 2),
                "nose_height": round(nose_height, 2),
                "nose_ratio": round(nose_ratio, 3)
            }
            
            # 嘴巴分析
            mouth_width = abs(points[54]['x'] - points[48]['x'])
            mouth_height = abs(points[57]['y'] - points[51]['y'])
            mouth_ratio = mouth_width / mouth_height
            
            if mouth_ratio > 4.0:
                mouth_analysis = "嘴巴较宽"
            elif mouth_ratio < 2.5:
                mouth_analysis = "嘴巴较小"
            else:
                mouth_analysis = "嘴巴大小适中"
            
            features["mouth"] = {
                "analysis": mouth_analysis,
                "mouth_width": round(mouth_width, 2),
                "mouth_height": round(mouth_height, 2),
                "mouth_ratio": round(mouth_ratio, 3)
            }
            
            return {
                "features": features,
                "analysis": "面部特征分析完成"
            }
            
        except Exception as e:
            self.logger.error(f"面部特征分析失败: {str(e)}")
            return {"features": {}, "analysis": "分析失败", "error": str(e)}
    
    def comprehensive_analysis(self, face_image: np.ndarray, landmarks: List[Dict]) -> Dict:
        """
        综合面部分析
        
        Args:
            face_image: 人脸图像
            landmarks: 面部特征点
            
        Returns:
            综合分析结果
        """
        start_time = time.time()
        
        try:
            results = {
                "symmetry": self.analyze_face_symmetry(landmarks),
                "face_shape": self.analyze_face_shape(landmarks),
                "skin_tone": self.analyze_skin_tone(face_image),
                "facial_features": self.analyze_facial_features(landmarks),
                "processing_time": 0.0
            }
            
            processing_time = time.time() - start_time
            results["processing_time"] = round(processing_time, 3)
            
            self.logger.info(f"综合面部分析完成，耗时 {processing_time:.3f}s")
            
            return results
            
        except Exception as e:
            self.logger.error(f"综合面部分析失败: {str(e)}")
            return {
                "error": str(e),
                "processing_time": time.time() - start_time
            }
