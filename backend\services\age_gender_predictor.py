"""
年龄性别预测服务
Age and Gender Prediction Service
"""

import cv2
import numpy as np
import tensorflow as tf
from typing import List, Dict, Tuple
import time
import os

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from utils.logger import app_logger
from utils.config import settings

class AgeGenderPredictor:
    """年龄性别预测器"""
    
    def __init__(self):
        """初始化年龄性别预测器"""
        self.logger = app_logger
        self.age_ranges = settings.AGE_RANGES
        self.age_model = None
        self.gender_model = None
        
        # 加载预训练模型
        self._load_models()
        
        self.logger.info("年龄性别预测器初始化完成")
    
    def _load_models(self):
        """加载年龄和性别预测模型"""
        try:
            # 加载年龄预测模型
            age_model_path = settings.AGE_MODEL_PATH
            if os.path.exists(age_model_path):
                self.age_model = tf.keras.models.load_model(age_model_path)
                self.logger.info(f"成功加载年龄模型: {age_model_path}")
            else:
                self.age_model = self._create_age_model()
                self.logger.warning(f"年龄模型文件不存在，使用默认模型: {age_model_path}")
            
            # 加载性别预测模型
            gender_model_path = settings.GENDER_MODEL_PATH
            if os.path.exists(gender_model_path):
                self.gender_model = tf.keras.models.load_model(gender_model_path)
                self.logger.info(f"成功加载性别模型: {gender_model_path}")
            else:
                self.gender_model = self._create_gender_model()
                self.logger.warning(f"性别模型文件不存在，使用默认模型: {gender_model_path}")
                
        except Exception as e:
            self.logger.error(f"加载年龄性别模型失败: {str(e)}")
            self.age_model = self._create_age_model()
            self.gender_model = self._create_gender_model()
    
    def _create_age_model(self):
        """创建默认的年龄预测模型"""
        try:
            model = tf.keras.Sequential([
                tf.keras.layers.Conv2D(32, (3, 3), activation='relu', input_shape=(64, 64, 3)),
                tf.keras.layers.MaxPooling2D((2, 2)),
                tf.keras.layers.Conv2D(64, (3, 3), activation='relu'),
                tf.keras.layers.MaxPooling2D((2, 2)),
                tf.keras.layers.Conv2D(128, (3, 3), activation='relu'),
                tf.keras.layers.MaxPooling2D((2, 2)),
                tf.keras.layers.Flatten(),
                tf.keras.layers.Dense(128, activation='relu'),
                tf.keras.layers.Dropout(0.5),
                tf.keras.layers.Dense(len(self.age_ranges), activation='softmax')
            ])
            
            model.compile(
                optimizer='adam',
                loss='categorical_crossentropy',
                metrics=['accuracy']
            )
            
            self.logger.info("创建默认年龄预测模型")
            return model
            
        except Exception as e:
            self.logger.error(f"创建默认年龄模型失败: {str(e)}")
            return None
    
    def _create_gender_model(self):
        """创建默认的性别预测模型"""
        try:
            model = tf.keras.Sequential([
                tf.keras.layers.Conv2D(32, (3, 3), activation='relu', input_shape=(64, 64, 3)),
                tf.keras.layers.MaxPooling2D((2, 2)),
                tf.keras.layers.Conv2D(64, (3, 3), activation='relu'),
                tf.keras.layers.MaxPooling2D((2, 2)),
                tf.keras.layers.Conv2D(128, (3, 3), activation='relu'),
                tf.keras.layers.MaxPooling2D((2, 2)),
                tf.keras.layers.Flatten(),
                tf.keras.layers.Dense(128, activation='relu'),
                tf.keras.layers.Dropout(0.5),
                tf.keras.layers.Dense(2, activation='softmax')  # 男性/女性
            ])
            
            model.compile(
                optimizer='adam',
                loss='categorical_crossentropy',
                metrics=['accuracy']
            )
            
            self.logger.info("创建默认性别预测模型")
            return model
            
        except Exception as e:
            self.logger.error(f"创建默认性别模型失败: {str(e)}")
            return None
    
    def preprocess_face(self, face_image: np.ndarray) -> np.ndarray:
        """
        预处理人脸图像用于年龄性别预测
        
        Args:
            face_image: 人脸图像
            
        Returns:
            预处理后的图像
        """
        try:
            # 确保是彩色图像
            if len(face_image.shape) == 2:
                face_image = cv2.cvtColor(face_image, cv2.COLOR_GRAY2BGR)
            elif face_image.shape[2] == 4:
                face_image = cv2.cvtColor(face_image, cv2.COLOR_BGRA2BGR)
            
            # 调整大小到64x64
            resized = cv2.resize(face_image, (64, 64))
            
            # 归一化
            normalized = resized.astype('float32') / 255.0
            
            # 添加批次维度
            processed = np.expand_dims(normalized, axis=0)
            
            return processed
            
        except Exception as e:
            self.logger.error(f"人脸预处理失败: {str(e)}")
            return None
    
    def predict_age(self, face_image: np.ndarray) -> Dict:
        """
        预测年龄
        
        Args:
            face_image: 人脸图像
            
        Returns:
            年龄预测结果
        """
        start_time = time.time()
        
        try:
            if self.age_model is None:
                return {
                    'age_range': 'unknown',
                    'confidence': 0.0,
                    'probabilities': {},
                    'processing_time': 0.0
                }
            
            # 预处理图像
            processed_image = self.preprocess_face(face_image)
            if processed_image is None:
                raise ValueError("图像预处理失败")
            
            # 进行预测
            predictions = self.age_model.predict(processed_image, verbose=0)
            probabilities = predictions[0]
            
            # 获取最高概率的年龄范围
            max_index = np.argmax(probabilities)
            age_range = self.age_ranges[max_index]
            confidence = float(probabilities[max_index])
            
            # 构建概率字典
            prob_dict = {}
            for i, range_label in enumerate(self.age_ranges):
                prob_dict[range_label] = float(probabilities[i])
            
            processing_time = time.time() - start_time
            
            result = {
                'age_range': age_range,
                'confidence': confidence,
                'probabilities': prob_dict,
                'processing_time': processing_time
            }
            
            self.logger.debug(f"年龄预测完成: {age_range} ({confidence:.3f}), 耗时 {processing_time:.3f}s")
            
            return result
            
        except Exception as e:
            self.logger.error(f"年龄预测失败: {str(e)}")
            return {
                'age_range': 'error',
                'confidence': 0.0,
                'probabilities': {},
                'processing_time': time.time() - start_time,
                'error': str(e)
            }
    
    def predict_gender(self, face_image: np.ndarray) -> Dict:
        """
        预测性别
        
        Args:
            face_image: 人脸图像
            
        Returns:
            性别预测结果
        """
        start_time = time.time()
        
        try:
            if self.gender_model is None:
                return {
                    'gender': 'unknown',
                    'gender_cn': '未知',
                    'confidence': 0.0,
                    'probabilities': {},
                    'processing_time': 0.0
                }
            
            # 预处理图像
            processed_image = self.preprocess_face(face_image)
            if processed_image is None:
                raise ValueError("图像预处理失败")
            
            # 进行预测
            predictions = self.gender_model.predict(processed_image, verbose=0)
            probabilities = predictions[0]
            
            # 性别标签
            gender_labels = ['male', 'female']
            gender_labels_cn = ['男性', '女性']
            
            # 获取最高概率的性别
            max_index = np.argmax(probabilities)
            gender = gender_labels[max_index]
            gender_cn = gender_labels_cn[max_index]
            confidence = float(probabilities[max_index])
            
            # 构建概率字典
            prob_dict = {}
            prob_dict_cn = {}
            for i, (label, label_cn) in enumerate(zip(gender_labels, gender_labels_cn)):
                prob_dict[label] = float(probabilities[i])
                prob_dict_cn[label_cn] = float(probabilities[i])
            
            processing_time = time.time() - start_time
            
            result = {
                'gender': gender,
                'gender_cn': gender_cn,
                'confidence': confidence,
                'probabilities': prob_dict,
                'probabilities_cn': prob_dict_cn,
                'processing_time': processing_time
            }
            
            self.logger.debug(f"性别预测完成: {gender_cn} ({confidence:.3f}), 耗时 {processing_time:.3f}s")
            
            return result
            
        except Exception as e:
            self.logger.error(f"性别预测失败: {str(e)}")
            return {
                'gender': 'error',
                'gender_cn': '错误',
                'confidence': 0.0,
                'probabilities': {},
                'processing_time': time.time() - start_time,
                'error': str(e)
            }
    
    def predict_age_gender(self, face_image: np.ndarray) -> Dict:
        """
        同时预测年龄和性别
        
        Args:
            face_image: 人脸图像
            
        Returns:
            年龄和性别预测结果
        """
        start_time = time.time()
        
        # 分别预测年龄和性别
        age_result = self.predict_age(face_image)
        gender_result = self.predict_gender(face_image)
        
        processing_time = time.time() - start_time
        
        # 合并结果
        combined_result = {
            'age': age_result,
            'gender': gender_result,
            'total_processing_time': processing_time
        }
        
        return combined_result
    
    def predict_batch(self, face_images: List[np.ndarray]) -> List[Dict]:
        """
        批量预测年龄和性别
        
        Args:
            face_images: 人脸图像列表
            
        Returns:
            预测结果列表
        """
        start_time = time.time()
        results = []
        
        for face_image in face_images:
            result = self.predict_age_gender(face_image)
            results.append(result)
        
        processing_time = time.time() - start_time
        self.logger.info(f"批量年龄性别预测完成: {len(face_images)} 张人脸，耗时 {processing_time:.3f}s")
        
        return results
    
    def draw_age_gender_result(self, image: np.ndarray, bbox: Dict, 
                              age_gender_result: Dict) -> np.ndarray:
        """
        在图像上绘制年龄性别预测结果
        
        Args:
            image: 原始图像
            bbox: 人脸边界框
            age_gender_result: 年龄性别预测结果
            
        Returns:
            绘制了结果的图像
        """
        result_image = image.copy()
        
        try:
            age_result = age_gender_result.get('age', {})
            gender_result = age_gender_result.get('gender', {})
            
            age_range = age_result.get('age_range', 'unknown')
            age_confidence = age_result.get('confidence', 0.0)
            
            gender_cn = gender_result.get('gender_cn', '未知')
            gender_confidence = gender_result.get('confidence', 0.0)
            
            # 绘制边界框
            cv2.rectangle(
                result_image,
                (bbox['x'], bbox['y']),
                (bbox['x'] + bbox['width'], bbox['y'] + bbox['height']),
                (255, 0, 255), 2  # 紫色
            )
            
            # 绘制年龄标签
            age_label = f"年龄: {age_range} ({age_confidence:.2f})"
            cv2.putText(
                result_image,
                age_label,
                (bbox['x'], bbox['y'] - 30),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.5, (255, 0, 255), 1
            )
            
            # 绘制性别标签
            gender_label = f"性别: {gender_cn} ({gender_confidence:.2f})"
            cv2.putText(
                result_image,
                gender_label,
                (bbox['x'], bbox['y'] - 10),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.5, (255, 0, 255), 1
            )
            
        except Exception as e:
            self.logger.error(f"绘制年龄性别结果失败: {str(e)}")
        
        return result_image
