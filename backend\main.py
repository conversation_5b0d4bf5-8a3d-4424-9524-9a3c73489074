"""
人脸面部分析系统 - 主程序入口
Facial Analysis System - Main Entry Point
"""

from fastapi import FastAPI, File, UploadFile, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
import uvicorn
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from api.routes import face_analysis, real_time, batch_processing
from services.face_detector import FaceDetector
from services.emotion_analyzer import EmotionAnalyzer
from services.age_gender_predictor import AgeGenderPredictor
from services.face_recognition_service import FaceRecognitionService
from utils.logger import setup_logger
from utils.config import settings

# 设置日志
logger = setup_logger()

# 创建FastAPI应用
app = FastAPI(
    title="人脸面部分析系统",
    description="功能完善的人脸面部分析API系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
if not os.path.exists("uploads"):
    os.makedirs("uploads")
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# 全局服务实例
face_detector = None
emotion_analyzer = None
age_gender_predictor = None
face_recognition_service = None

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化服务"""
    global face_detector, emotion_analyzer, age_gender_predictor, face_recognition_service
    
    logger.info("正在初始化人脸分析服务...")
    
    try:
        # 初始化各个服务
        face_detector = FaceDetector()
        emotion_analyzer = EmotionAnalyzer()
        age_gender_predictor = AgeGenderPredictor()
        face_recognition_service = FaceRecognitionService()
        
        logger.info("所有服务初始化完成！")
        
    except Exception as e:
        logger.error(f"服务初始化失败: {str(e)}")
        raise e

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理资源"""
    logger.info("正在关闭人脸分析服务...")

# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "message": "人脸面部分析系统运行正常",
        "version": "1.0.0"
    }

# 系统信息端点
@app.get("/info")
async def system_info():
    """获取系统信息"""
    return {
        "system": "人脸面部分析系统",
        "version": "1.0.0",
        "features": [
            "人脸检测",
            "表情分析", 
            "年龄性别预测",
            "面部特征点检测",
            "人脸识别",
            "实时视频分析",
            "批量处理"
        ],
        "status": "运行中"
    }

# 注册路由
app.include_router(face_analysis.router, prefix="/api/v1", tags=["面部分析"])
app.include_router(real_time.router, prefix="/api/v1", tags=["实时分析"])
app.include_router(batch_processing.router, prefix="/api/v1", tags=["批量处理"])

# WebSocket连接管理
class ConnectionManager:
    def __init__(self):
        self.active_connections: list[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            await connection.send_text(message)

manager = ConnectionManager()

@app.websocket("/ws/realtime")
async def websocket_endpoint(websocket: WebSocket):
    """实时分析WebSocket端点"""
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            # 处理实时数据
            await manager.send_personal_message(f"收到数据: {data}", websocket)
    except WebSocketDisconnect:
        manager.disconnect(websocket)

# 错误处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content={"message": exc.detail, "status": "error"}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"未处理的异常: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"message": "内部服务器错误", "status": "error"}
    )

if __name__ == "__main__":
    logger.info("启动人脸面部分析系统...")
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
