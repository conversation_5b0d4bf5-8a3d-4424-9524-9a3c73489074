"""
3D人脸重建服务
3D Face Reconstruction Service
"""

import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional
import time
import math

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from utils.logger import app_logger
from utils.config import settings

class Face3DReconstruction:
    """3D人脸重建器"""
    
    def __init__(self):
        """初始化3D重建器"""
        self.logger = app_logger
        self.logger.info("3D人脸重建器初始化完成")
    
    def estimate_pose(self, landmarks: List[Dict]) -> Dict:
        """
        估计头部姿态
        
        Args:
            landmarks: 面部特征点
            
        Returns:
            头部姿态信息
        """
        try:
            if not landmarks:
                return {"error": "无特征点数据"}
            
            points = landmarks[0]['landmarks']
            if len(points) < 68:
                return {"error": "特征点不足"}
            
            # 选择关键点进行姿态估计
            # 鼻尖、下巴、左眼角、右眼角、嘴角
            image_points = np.array([
                [points[30]['x'], points[30]['y']],  # 鼻尖
                [points[8]['x'], points[8]['y']],    # 下巴
                [points[36]['x'], points[36]['y']],  # 左眼外角
                [points[45]['x'], points[45]['y']],  # 右眼外角
                [points[48]['x'], points[48]['y']],  # 左嘴角
                [points[54]['x'], points[54]['y']]   # 右嘴角
            ], dtype="double")
            
            # 3D模型点 (标准人脸模型)
            model_points = np.array([
                (0.0, 0.0, 0.0),             # 鼻尖
                (0.0, -330.0, -65.0),        # 下巴
                (-225.0, 170.0, -135.0),     # 左眼外角
                (225.0, 170.0, -135.0),      # 右眼外角
                (-150.0, -150.0, -125.0),    # 左嘴角
                (150.0, -150.0, -125.0)      # 右嘴角
            ])
            
            # 相机参数 (假设)
            focal_length = 500
            center = (320, 240)
            camera_matrix = np.array([
                [focal_length, 0, center[0]],
                [0, focal_length, center[1]],
                [0, 0, 1]
            ], dtype="double")
            
            # 畸变系数
            dist_coeffs = np.zeros((4, 1))
            
            # 求解PnP问题
            success, rotation_vector, translation_vector = cv2.solvePnP(
                model_points, image_points, camera_matrix, dist_coeffs
            )
            
            if success:
                # 转换为欧拉角
                rotation_matrix, _ = cv2.Rodrigues(rotation_vector)
                
                # 计算欧拉角 (度)
                sy = math.sqrt(rotation_matrix[0, 0] * rotation_matrix[0, 0] + 
                              rotation_matrix[1, 0] * rotation_matrix[1, 0])
                
                singular = sy < 1e-6
                
                if not singular:
                    x = math.atan2(rotation_matrix[2, 1], rotation_matrix[2, 2])
                    y = math.atan2(-rotation_matrix[2, 0], sy)
                    z = math.atan2(rotation_matrix[1, 0], rotation_matrix[0, 0])
                else:
                    x = math.atan2(-rotation_matrix[1, 2], rotation_matrix[1, 1])
                    y = math.atan2(-rotation_matrix[2, 0], sy)
                    z = 0
                
                # 转换为度数
                pitch = math.degrees(x)  # 俯仰角
                yaw = math.degrees(y)    # 偏航角
                roll = math.degrees(z)   # 翻滚角
                
                # 姿态分类
                pose_description = self._classify_pose(pitch, yaw, roll)
                
                return {
                    "success": True,
                    "euler_angles": {
                        "pitch": round(pitch, 2),
                        "yaw": round(yaw, 2),
                        "roll": round(roll, 2)
                    },
                    "pose_description": pose_description,
                    "rotation_vector": rotation_vector.flatten().tolist(),
                    "translation_vector": translation_vector.flatten().tolist()
                }
            else:
                return {"error": "姿态估计失败"}
                
        except Exception as e:
            self.logger.error(f"头部姿态估计失败: {str(e)}")
            return {"error": str(e)}
    
    def _classify_pose(self, pitch: float, yaw: float, roll: float) -> str:
        """分类头部姿态"""
        # 阈值设定
        threshold = 15
        
        pose_parts = []
        
        # 俯仰角分析
        if pitch > threshold:
            pose_parts.append("低头")
        elif pitch < -threshold:
            pose_parts.append("抬头")
        
        # 偏航角分析
        if yaw > threshold:
            pose_parts.append("向右转")
        elif yaw < -threshold:
            pose_parts.append("向左转")
        
        # 翻滚角分析
        if roll > threshold:
            pose_parts.append("向右倾斜")
        elif roll < -threshold:
            pose_parts.append("向左倾斜")
        
        if not pose_parts:
            return "正面"
        else:
            return "、".join(pose_parts)
    
    def generate_depth_map(self, image: np.ndarray, landmarks: List[Dict]) -> Dict:
        """
        生成深度图
        
        Args:
            image: 输入图像
            landmarks: 面部特征点
            
        Returns:
            深度图信息
        """
        try:
            if not landmarks:
                return {"error": "无特征点数据"}
            
            points = landmarks[0]['landmarks']
            if len(points) < 68:
                return {"error": "特征点不足"}
            
            h, w = image.shape[:2]
            depth_map = np.zeros((h, w), dtype=np.float32)
            
            # 基于特征点生成简化深度图
            # 鼻尖最突出，眼部和嘴部次之，脸颊最深
            
            # 定义深度值 (0-255, 255为最近)
            depth_values = {
                'nose': 255,      # 鼻子最突出
                'eyes': 200,      # 眼部
                'mouth': 180,     # 嘴部
                'face': 150,      # 面部
                'background': 0   # 背景
            }
            
            # 鼻子区域 (点27-35)
            nose_points = np.array([[points[i]['x'], points[i]['y']] for i in range(27, 36)], np.int32)
            cv2.fillPoly(depth_map, [nose_points], depth_values['nose'])
            
            # 左眼区域 (点36-41)
            left_eye_points = np.array([[points[i]['x'], points[i]['y']] for i in range(36, 42)], np.int32)
            cv2.fillPoly(depth_map, [left_eye_points], depth_values['eyes'])
            
            # 右眼区域 (点42-47)
            right_eye_points = np.array([[points[i]['x'], points[i]['y']] for i in range(42, 48)], np.int32)
            cv2.fillPoly(depth_map, [right_eye_points], depth_values['eyes'])
            
            # 嘴部区域 (点48-67)
            mouth_points = np.array([[points[i]['x'], points[i]['y']] for i in range(48, 68)], np.int32)
            cv2.fillPoly(depth_map, [mouth_points], depth_values['mouth'])
            
            # 面部轮廓 (点0-16)
            face_contour = np.array([[points[i]['x'], points[i]['y']] for i in range(17)], np.int32)
            cv2.fillPoly(depth_map, [face_contour], depth_values['face'])
            
            # 应用高斯模糊使深度图更平滑
            depth_map = cv2.GaussianBlur(depth_map, (15, 15), 0)
            
            # 转换为彩色深度图用于可视化
            depth_colored = cv2.applyColorMap(depth_map.astype(np.uint8), cv2.COLORMAP_JET)
            
            return {
                "success": True,
                "depth_map": depth_map,
                "depth_colored": depth_colored,
                "statistics": {
                    "min_depth": float(np.min(depth_map)),
                    "max_depth": float(np.max(depth_map)),
                    "mean_depth": float(np.mean(depth_map))
                }
            }
            
        except Exception as e:
            self.logger.error(f"深度图生成失败: {str(e)}")
            return {"error": str(e)}
    
    def create_3d_wireframe(self, landmarks: List[Dict]) -> Dict:
        """
        创建3D线框模型
        
        Args:
            landmarks: 面部特征点
            
        Returns:
            3D线框信息
        """
        try:
            if not landmarks:
                return {"error": "无特征点数据"}
            
            points = landmarks[0]['landmarks']
            if len(points) < 68:
                return {"error": "特征点不足"}
            
            # 定义连接关系 (哪些点之间需要连线)
            connections = [
                # 面部轮廓
                list(range(0, 17)),
                # 左眉毛
                list(range(17, 22)),
                # 右眉毛
                list(range(22, 27)),
                # 鼻梁
                list(range(27, 31)),
                # 鼻孔
                list(range(31, 36)),
                # 左眼
                list(range(36, 42)) + [36],  # 闭合
                # 右眼
                list(range(42, 48)) + [42],  # 闭合
                # 外嘴唇
                list(range(48, 60)) + [48],  # 闭合
                # 内嘴唇
                list(range(60, 68)) + [60]   # 闭合
            ]
            
            # 创建线框图像
            wireframe_image = np.zeros((400, 400, 3), dtype=np.uint8)
            
            # 计算缩放和偏移
            all_x = [p['x'] for p in points]
            all_y = [p['y'] for p in points]
            
            min_x, max_x = min(all_x), max(all_x)
            min_y, max_y = min(all_y), max(all_y)
            
            # 缩放到适合显示的大小
            scale = min(350 / (max_x - min_x), 350 / (max_y - min_y))
            offset_x = (400 - (max_x - min_x) * scale) // 2 - min_x * scale
            offset_y = (400 - (max_y - min_y) * scale) // 2 - min_y * scale
            
            # 绘制连接线
            colors = [
                (255, 255, 255),  # 面部轮廓 - 白色
                (0, 255, 0),      # 左眉毛 - 绿色
                (0, 255, 0),      # 右眉毛 - 绿色
                (255, 0, 0),      # 鼻梁 - 红色
                (255, 0, 0),      # 鼻孔 - 红色
                (0, 0, 255),      # 左眼 - 蓝色
                (0, 0, 255),      # 右眼 - 蓝色
                (255, 255, 0),    # 外嘴唇 - 黄色
                (255, 0, 255)     # 内嘴唇 - 紫色
            ]
            
            for i, connection in enumerate(connections):
                color = colors[i % len(colors)]
                for j in range(len(connection) - 1):
                    pt1_idx = connection[j]
                    pt2_idx = connection[j + 1]
                    
                    pt1 = (
                        int(points[pt1_idx]['x'] * scale + offset_x),
                        int(points[pt1_idx]['y'] * scale + offset_y)
                    )
                    pt2 = (
                        int(points[pt2_idx]['x'] * scale + offset_x),
                        int(points[pt2_idx]['y'] * scale + offset_y)
                    )
                    
                    cv2.line(wireframe_image, pt1, pt2, color, 2)
            
            # 绘制特征点
            for point in points:
                pt = (
                    int(point['x'] * scale + offset_x),
                    int(point['y'] * scale + offset_y)
                )
                cv2.circle(wireframe_image, pt, 2, (255, 255, 255), -1)
            
            return {
                "success": True,
                "wireframe_image": wireframe_image,
                "connections": connections,
                "scale_info": {
                    "scale": scale,
                    "offset_x": offset_x,
                    "offset_y": offset_y
                }
            }
            
        except Exception as e:
            self.logger.error(f"3D线框创建失败: {str(e)}")
            return {"error": str(e)}
    
    def comprehensive_3d_analysis(self, image: np.ndarray, landmarks: List[Dict]) -> Dict:
        """
        综合3D分析
        
        Args:
            image: 输入图像
            landmarks: 面部特征点
            
        Returns:
            综合3D分析结果
        """
        start_time = time.time()
        
        try:
            results = {
                "pose_estimation": self.estimate_pose(landmarks),
                "depth_map": self.generate_depth_map(image, landmarks),
                "wireframe": self.create_3d_wireframe(landmarks),
                "processing_time": 0.0
            }
            
            processing_time = time.time() - start_time
            results["processing_time"] = round(processing_time, 3)
            
            self.logger.info(f"综合3D分析完成，耗时 {processing_time:.3f}s")
            
            return results
            
        except Exception as e:
            self.logger.error(f"综合3D分析失败: {str(e)}")
            return {
                "error": str(e),
                "processing_time": time.time() - start_time
            }
