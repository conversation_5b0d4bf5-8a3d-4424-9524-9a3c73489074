import React, { useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { useDropzone } from 'react-dropzone'
import { Upload, FolderOpen, Loader2, Download, Trash2, Eye } from 'lucide-react'
import toast from 'react-hot-toast'

const BatchPage = () => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [batchResults, setBatchResults] = useState<any>(null)
  const [progress, setProgress] = useState(0)

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles = [...selectedFiles, ...acceptedFiles]
    if (newFiles.length > 50) {
      toast.error('最多只能选择50张图片')
      return
    }
    setSelectedFiles(newFiles)
    toast.success(`已添加 ${acceptedFiles.length} 张图片`)
  }, [selectedFiles])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.bmp', '.tiff']
    },
    multiple: true,
    maxSize: 10 * 1024 * 1024 // 10MB per file
  })

  const removeFile = (index: number) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index)
    setSelectedFiles(newFiles)
    toast.success('文件已移除')
  }

  const clearAll = () => {
    setSelectedFiles([])
    setBatchResults(null)
    setProgress(0)
    toast.success('已清空所有文件')
  }

  const startBatchProcessing = async () => {
    if (selectedFiles.length === 0) {
      toast.error('请先选择图片文件')
      return
    }

    setIsProcessing(true)
    setProgress(0)

    try {
      const formData = new FormData()
      selectedFiles.forEach(file => {
        formData.append('files', file)
      })

      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return prev
          }
          return prev + 10
        })
      }, 500)

      const response = await fetch('/api/v1/batch/analyze', {
        method: 'POST',
        body: formData
      })

      clearInterval(progressInterval)
      setProgress(100)

      if (!response.ok) {
        throw new Error('批量处理失败')
      }

      const result = await response.json()
      setBatchResults(result)
      toast.success('批量处理完成！')

    } catch (error) {
      console.error('Batch processing error:', error)
      toast.error('批量处理失败，请重试')
    } finally {
      setIsProcessing(false)
    }
  }

  const downloadResults = () => {
    if (batchResults) {
      const dataStr = JSON.stringify(batchResults, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)
      const link = document.createElement('a')
      link.href = url
      link.download = `batch_analysis_results_${Date.now()}.json`
      link.click()
      URL.revokeObjectURL(url)
    }
  }

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h1 className="text-3xl font-bold text-slate-900 mb-4">
          批量处理
        </h1>
        <p className="text-lg text-slate-600">
          一次性上传多张图片进行批量人脸分析，提高工作效率
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 左侧：文件上传和管理 */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="lg:col-span-2 space-y-6"
        >
          {/* 上传区域 */}
          <div className="card p-6">
            <h2 className="text-xl font-semibold text-slate-900 mb-4">
              选择图片文件
            </h2>
            
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200 ${
                isDragActive
                  ? 'border-primary-500 bg-primary-50'
                  : 'border-slate-300 hover:border-primary-400 hover:bg-slate-50'
              }`}
            >
              <input {...getInputProps()} />
              <div className="space-y-4">
                <div className="mx-auto w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center">
                  <FolderOpen className="w-8 h-8 text-slate-400" />
                </div>
                <div>
                  <p className="text-lg font-medium text-slate-900">
                    {isDragActive ? '释放文件到这里' : '拖拽多张图片到这里'}
                  </p>
                  <p className="text-sm text-slate-500 mt-1">
                    或点击选择文件 (最多50张，每张不超过10MB)
                  </p>
                </div>
              </div>
            </div>

            {/* 文件统计 */}
            <div className="flex items-center justify-between mt-4 p-4 bg-slate-50 rounded-lg">
              <div className="text-sm text-slate-600">
                已选择 <span className="font-medium text-slate-900">{selectedFiles.length}</span> 张图片
              </div>
              {selectedFiles.length > 0 && (
                <button
                  onClick={clearAll}
                  className="text-red-600 hover:text-red-700 text-sm font-medium"
                >
                  清空所有
                </button>
              )}
            </div>
          </div>

          {/* 文件列表 */}
          {selectedFiles.length > 0 && (
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-slate-900 mb-4">
                文件列表
              </h3>
              
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {selectedFiles.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                        <Eye className="w-4 h-4 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium text-slate-900 text-sm">
                          {file.name}
                        </div>
                        <div className="text-xs text-slate-500">
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={() => removeFile(index)}
                      className="text-red-500 hover:text-red-700 p-1"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>

              {/* 处理按钮 */}
              <button
                onClick={startBatchProcessing}
                disabled={isProcessing || selectedFiles.length === 0}
                className="btn-primary w-full mt-4 py-3"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    处理中... {progress}%
                  </>
                ) : (
                  <>
                    <Upload className="w-5 h-5 mr-2" />
                    开始批量处理
                  </>
                )}
              </button>

              {/* 进度条 */}
              {isProcessing && (
                <div className="mt-4">
                  <div className="w-full bg-slate-200 rounded-full h-2">
                    <div
                      className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </div>
          )}
        </motion.div>

        {/* 右侧：处理结果 */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-6"
        >
          {batchResults ? (
            <>
              {/* 处理统计 */}
              <div className="card p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-slate-900">
                    处理结果
                  </h3>
                  <button
                    onClick={downloadResults}
                    className="btn-secondary text-sm"
                  >
                    <Download className="w-4 h-4 mr-1" />
                    导出
                  </button>
                </div>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <div className="text-xl font-bold text-blue-600">
                        {batchResults.statistics?.total_files || 0}
                      </div>
                      <div className="text-xs text-blue-700">总文件数</div>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <div className="text-xl font-bold text-green-600">
                        {batchResults.statistics?.successful || 0}
                      </div>
                      <div className="text-xs text-green-700">成功处理</div>
                    </div>
                  </div>
                  
                  <div className="text-center p-3 bg-slate-50 rounded-lg">
                    <div className="text-lg font-bold text-slate-900">
                      {batchResults.processing_time?.toFixed(2) || 0}s
                    </div>
                    <div className="text-xs text-slate-600">总处理时间</div>
                  </div>
                </div>
              </div>

              {/* 详细结果 */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-slate-900 mb-4">
                  详细结果
                </h3>
                
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {batchResults.results?.map((result: any, index: number) => (
                    <div key={index} className="p-3 bg-slate-50 rounded-lg">
                      <div className="font-medium text-slate-900 text-sm mb-1">
                        {result.filename}
                      </div>
                      <div className="flex items-center justify-between text-xs">
                        <span className={`badge ${result.success ? 'badge-success' : 'badge-error'}`}>
                          {result.success ? '成功' : '失败'}
                        </span>
                        {result.success && (
                          <span className="text-slate-600">
                            {result.analysis?.faces_detected || 0} 张人脸
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          ) : (
            <div className="card p-12 text-center">
              <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FolderOpen className="w-8 h-8 text-slate-400" />
              </div>
              <h3 className="text-lg font-medium text-slate-900 mb-2">
                等待处理
              </h3>
              <p className="text-slate-600 text-sm">
                选择图片文件并开始批量处理
              </p>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  )
}

export default BatchPage
