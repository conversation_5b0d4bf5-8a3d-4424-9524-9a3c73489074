"""
高级功能API路由
Advanced Features API Routes
"""

from fastapi import APIRouter, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
import cv2
import numpy as np
from typing import List, Optional, Dict
import io
from PIL import Image
import base64
import json
import time

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from services.face_detector import FaceDetector
from services.advanced_analysis import AdvancedAnalysis
from services.image_enhancement import ImageEnhancement
from services.analytics_service import AnalyticsService
from services.face_3d_reconstruction import Face3DReconstruction
from services.emotion_ai import EmotionAI
from services.recommendation_engine import RecommendationEngine
from utils.logger import app_logger
from utils.config import settings

router = APIRouter()

# 全局服务实例
face_detector = None
advanced_analysis = None
image_enhancement = None
analytics_service = None
face_3d = None
emotion_ai = None
recommendation_engine = None

def get_services():
    """获取服务实例"""
    global face_detector, advanced_analysis, image_enhancement, analytics_service, face_3d, emotion_ai, recommendation_engine

    if face_detector is None:
        face_detector = FaceDetector()
    if advanced_analysis is None:
        advanced_analysis = AdvancedAnalysis()
    if image_enhancement is None:
        image_enhancement = ImageEnhancement()
    if analytics_service is None:
        analytics_service = AnalyticsService()
    if face_3d is None:
        face_3d = Face3DReconstruction()
    if emotion_ai is None:
        emotion_ai = EmotionAI()
    if recommendation_engine is None:
        recommendation_engine = RecommendationEngine()

    return face_detector, advanced_analysis, image_enhancement, analytics_service, face_3d, emotion_ai, recommendation_engine

def process_uploaded_image(file: UploadFile) -> np.ndarray:
    """处理上传的图像文件"""
    try:
        contents = file.file.read()
        nparr = np.frombuffer(contents, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is None:
            raise ValueError("无法解码图像文件")
        
        return image
        
    except Exception as e:
        app_logger.error(f"处理上传图像失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"图像处理失败: {str(e)}")

def encode_image_to_base64(image: np.ndarray) -> str:
    """将图像编码为base64字符串"""
    try:
        _, buffer = cv2.imencode('.jpg', image)
        image_base64 = base64.b64encode(buffer).decode('utf-8')
        return f"data:image/jpeg;base64,{image_base64}"
    except Exception as e:
        app_logger.error(f"图像编码失败: {str(e)}")
        return ""

@router.post("/advanced/comprehensive-analysis")
async def comprehensive_face_analysis(file: UploadFile = File(...)):
    """
    综合面部分析
    包括对称性、面部形状、肤色、面部特征等高级分析
    """
    start_time = time.time()
    
    try:
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="请上传图像文件")
        
        detector, advanced, _, analytics, _, _, _ = get_services()
        
        # 处理图像
        image = process_uploaded_image(file)
        original_image = image.copy()
        
        # 基础检测
        faces = detector.detect_faces(image)
        if not faces:
            return JSONResponse(content={
                "success": False,
                "message": "未检测到人脸",
                "processing_time": time.time() - start_time
            })
        
        landmarks = detector.detect_landmarks(image)
        cropped_faces = detector.crop_faces(image, faces)
        
        # 高级分析
        comprehensive_results = []
        for i, cropped_face in enumerate(cropped_faces):
            result = advanced.comprehensive_analysis(cropped_face, landmarks)
            comprehensive_results.append(result)
        
        # 记录分析数据
        analytics_data = {
            "type": "comprehensive_analysis",
            "faces": faces,
            "success": True,
            "processing_time": time.time() - start_time,
            "image_width": image.shape[1],
            "image_height": image.shape[0]
        }
        analytics.record_analysis(analytics_data)
        
        processing_time = time.time() - start_time
        
        response = {
            "success": True,
            "message": f"综合分析完成，检测到 {len(faces)} 张人脸",
            "processing_time": processing_time,
            "results": {
                "basic_detection": {
                    "faces": faces,
                    "landmarks": landmarks
                },
                "advanced_analysis": comprehensive_results,
                "statistics": {
                    "total_faces": len(faces),
                    "analysis_types": ["symmetry", "face_shape", "skin_tone", "facial_features"]
                }
            }
        }
        
        app_logger.info(f"综合面部分析完成: {len(faces)} 张人脸，耗时 {processing_time:.3f}s")
        
        return JSONResponse(content=response)
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"综合面部分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")

@router.post("/advanced/beauty-filter")
async def apply_beauty_filter(
    file: UploadFile = File(...),
    settings: str = Form(default='{}')
):
    """
    应用美颜滤镜
    """
    start_time = time.time()
    
    try:
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="请上传图像文件")
        
        detector, _, enhancer, analytics, _, _, _ = get_services()
        
        # 解析美颜设置
        try:
            beauty_settings = json.loads(settings)
        except:
            beauty_settings = {
                "skin_smoothing": 0.5,
                "brightness": 10,
                "contrast": 1.1,
                "saturation": 1.2,
                "eye_enhancement": 0.3,
                "teeth_whitening": 0.3,
                "blemish_removal": 0.4
            }
        
        # 处理图像
        image = process_uploaded_image(file)
        
        # 检测人脸和特征点
        faces = detector.detect_faces(image)
        landmarks = detector.detect_landmarks(image)
        
        # 应用美颜滤镜
        beauty_result = enhancer.apply_beauty_filter(image, landmarks, beauty_settings)
        
        if beauty_result["success"]:
            enhanced_image_base64 = encode_image_to_base64(beauty_result["enhanced_image"])
            original_image_base64 = encode_image_to_base64(image)
            
            # 记录分析数据
            analytics_data = {
                "type": "beauty_filter",
                "faces": faces,
                "success": True,
                "processing_time": beauty_result["processing_time"],
                "image_width": image.shape[1],
                "image_height": image.shape[0]
            }
            analytics.record_analysis(analytics_data)
            
            response = {
                "success": True,
                "message": "美颜滤镜应用成功",
                "processing_time": beauty_result["processing_time"],
                "settings_applied": beauty_result["settings_applied"],
                "images": {
                    "original": original_image_base64,
                    "enhanced": enhanced_image_base64
                },
                "faces_detected": len(faces)
            }
        else:
            response = {
                "success": False,
                "message": "美颜滤镜应用失败",
                "error": beauty_result.get("error", "未知错误"),
                "processing_time": beauty_result["processing_time"]
            }
        
        return JSONResponse(content=response)
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"美颜滤镜应用失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")

@router.get("/analytics/daily-stats")
async def get_daily_statistics(days: int = 7):
    """获取每日统计数据"""
    try:
        _, _, _, analytics, _, _, _ = get_services()
        stats = analytics.get_daily_statistics(days)
        
        return JSONResponse(content={
            "success": True,
            "data": stats
        })
        
    except Exception as e:
        app_logger.error(f"获取每日统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")

@router.get("/analytics/emotion-stats")
async def get_emotion_statistics(days: int = 30):
    """获取表情统计数据"""
    try:
        _, _, _, analytics, _, _, _ = get_services()
        stats = analytics.get_emotion_statistics(days)
        
        return JSONResponse(content={
            "success": True,
            "data": stats
        })
        
    except Exception as e:
        app_logger.error(f"获取表情统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")

@router.get("/analytics/demographic-stats")
async def get_demographic_statistics(days: int = 30):
    """获取人口统计数据"""
    try:
        _, _, _, analytics, _, _, _ = get_services()
        stats = analytics.get_demographic_statistics(days)
        
        return JSONResponse(content={
            "success": True,
            "data": stats
        })
        
    except Exception as e:
        app_logger.error(f"获取人口统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")

@router.get("/analytics/performance-stats")
async def get_performance_statistics(days: int = 7):
    """获取性能统计数据"""
    try:
        _, _, _, analytics, _, _, _ = get_services()
        stats = analytics.get_performance_statistics(days)
        
        return JSONResponse(content={
            "success": True,
            "data": stats
        })
        
    except Exception as e:
        app_logger.error(f"获取性能统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")

@router.get("/analytics/comprehensive-report")
async def get_comprehensive_report(days: int = 30):
    """获取综合分析报告"""
    try:
        _, _, _, analytics, _, _, _ = get_services()
        report = analytics.get_comprehensive_report(days)
        
        return JSONResponse(content={
            "success": True,
            "data": report
        })
        
    except Exception as e:
        app_logger.error(f"获取综合报告失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取报告失败: {str(e)}")

@router.post("/advanced/face-comparison")
async def compare_two_faces(
    file1: UploadFile = File(...),
    file2: UploadFile = File(...)
):
    """
    比较两张人脸的相似度
    """
    start_time = time.time()
    
    try:
        if not file1.content_type.startswith('image/') or not file2.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="请上传图像文件")
        
        detector, advanced, _, analytics, _, _, _ = get_services()
        
        # 处理两张图像
        image1 = process_uploaded_image(file1)
        image2 = process_uploaded_image(file2)
        
        # 检测人脸
        faces1 = detector.detect_faces(image1)
        faces2 = detector.detect_faces(image2)
        
        if not faces1 or not faces2:
            return JSONResponse(content={
                "success": False,
                "message": "其中一张或两张图片未检测到人脸",
                "processing_time": time.time() - start_time
            })
        
        # 裁剪人脸
        cropped_faces1 = detector.crop_faces(image1, faces1)
        cropped_faces2 = detector.crop_faces(image2, faces2)
        
        if not cropped_faces1 or not cropped_faces2:
            return JSONResponse(content={
                "success": False,
                "message": "人脸裁剪失败",
                "processing_time": time.time() - start_time
            })
        
        # 简单的相似度计算（基于直方图比较）
        face1 = cropped_faces1[0]
        face2 = cropped_faces2[0]
        
        # 调整大小到相同尺寸
        face1_resized = cv2.resize(face1, (100, 100))
        face2_resized = cv2.resize(face2, (100, 100))
        
        # 计算直方图
        hist1 = cv2.calcHist([face1_resized], [0, 1, 2], None, [50, 50, 50], [0, 256, 0, 256, 0, 256])
        hist2 = cv2.calcHist([face2_resized], [0, 1, 2], None, [50, 50, 50], [0, 256, 0, 256, 0, 256])
        
        # 计算相似度
        similarity = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
        similarity_percentage = max(0, similarity * 100)
        
        # 判断是否为同一人
        is_same_person = similarity_percentage > 70  # 阈值可调整
        
        processing_time = time.time() - start_time
        
        # 记录分析数据
        analytics_data = {
            "type": "face_comparison",
            "faces": faces1 + faces2,
            "success": True,
            "processing_time": processing_time,
            "image_width": max(image1.shape[1], image2.shape[1]),
            "image_height": max(image1.shape[0], image2.shape[0])
        }
        analytics.record_analysis(analytics_data)
        
        response = {
            "success": True,
            "message": "人脸比较完成",
            "processing_time": processing_time,
            "results": {
                "similarity_percentage": round(similarity_percentage, 2),
                "is_same_person": is_same_person,
                "confidence": "high" if similarity_percentage > 80 else "medium" if similarity_percentage > 60 else "low",
                "faces_detected": {
                    "image1": len(faces1),
                    "image2": len(faces2)
                }
            }
        }
        
        return JSONResponse(content=response)
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"人脸比较失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"比较失败: {str(e)}")

@router.post("/advanced/3d-analysis")
async def face_3d_analysis(file: UploadFile = File(...)):
    """
    3D人脸分析
    包括头部姿态估计、深度图生成、3D线框模型
    """
    start_time = time.time()

    try:
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="请上传图像文件")

        detector, _, _, analytics, face_3d, _, _ = get_services()

        # 处理图像
        image = process_uploaded_image(file)

        # 检测人脸和特征点
        faces = detector.detect_faces(image)
        if not faces:
            return JSONResponse(content={
                "success": False,
                "message": "未检测到人脸",
                "processing_time": time.time() - start_time
            })

        landmarks = detector.detect_landmarks(image)

        # 进行3D分析
        analysis_3d = face_3d.comprehensive_3d_analysis(image, landmarks)

        # 编码图像结果
        results_with_images = {}
        if analysis_3d.get('depth_map', {}).get('success'):
            depth_colored = analysis_3d['depth_map']['depth_colored']
            results_with_images['depth_map'] = encode_image_to_base64(depth_colored)

        if analysis_3d.get('wireframe', {}).get('success'):
            wireframe_image = analysis_3d['wireframe']['wireframe_image']
            results_with_images['wireframe'] = encode_image_to_base64(wireframe_image)

        # 记录分析数据
        analytics_data = {
            "type": "3d_analysis",
            "faces": faces,
            "success": True,
            "processing_time": analysis_3d.get('processing_time', 0),
            "image_width": image.shape[1],
            "image_height": image.shape[0]
        }
        analytics.record_analysis(analytics_data)

        response = {
            "success": True,
            "message": "3D人脸分析完成",
            "processing_time": analysis_3d.get('processing_time', 0),
            "results": {
                "pose_estimation": analysis_3d.get('pose_estimation', {}),
                "depth_statistics": analysis_3d.get('depth_map', {}).get('statistics', {}),
                "wireframe_info": analysis_3d.get('wireframe', {}).get('scale_info', {}),
                "images": results_with_images
            },
            "faces_detected": len(faces)
        }

        return JSONResponse(content=response)

    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"3D人脸分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")

@router.post("/advanced/emotion-ai")
async def emotion_ai_analysis(file: UploadFile = File(...)):
    """
    情感AI分析
    包括微表情分析、情感强度分析
    """
    start_time = time.time()

    try:
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="请上传图像文件")

        detector, _, _, analytics, _, emotion_ai, _ = get_services()

        # 处理图像
        image = process_uploaded_image(file)

        # 检测人脸和特征点
        faces = detector.detect_faces(image)
        if not faces:
            return JSONResponse(content={
                "success": False,
                "message": "未检测到人脸",
                "processing_time": time.time() - start_time
            })

        landmarks = detector.detect_landmarks(image)

        # 基础情感分析 (这里需要调用情感分析服务)
        # 为了演示，我们创建一个模拟的情感结果
        emotions = [{"emotion": "happy", "confidence": 0.85}]

        # 进行情感AI分析
        emotion_analysis = emotion_ai.comprehensive_emotion_analysis(emotions, landmarks)

        # 记录分析数据
        analytics_data = {
            "type": "emotion_ai",
            "faces": faces,
            "emotions": [e.get('emotion', 'unknown') for e in emotions],
            "success": True,
            "processing_time": emotion_analysis.get('processing_time', 0),
            "image_width": image.shape[1],
            "image_height": image.shape[0]
        }
        analytics.record_analysis(analytics_data)

        response = {
            "success": True,
            "message": "情感AI分析完成",
            "processing_time": emotion_analysis.get('processing_time', 0),
            "results": {
                "micro_expressions": emotion_analysis.get('micro_expressions', {}),
                "emotion_intensity": emotion_analysis.get('emotion_intensity', {}),
                "base_emotions": emotions
            },
            "faces_detected": len(faces)
        }

        return JSONResponse(content=response)

    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"情感AI分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")

@router.post("/advanced/smart-recommendations")
async def smart_recommendations(file: UploadFile = File(...)):
    """
    智能推荐
    包括美颜设置推荐、拍照建议、化妆风格推荐
    """
    start_time = time.time()

    try:
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="请上传图像文件")

        detector, advanced, _, analytics, _, _, recommendation = get_services()

        # 处理图像
        image = process_uploaded_image(file)

        # 检测人脸和特征点
        faces = detector.detect_faces(image)
        if not faces:
            return JSONResponse(content={
                "success": False,
                "message": "未检测到人脸",
                "processing_time": time.time() - start_time
            })

        landmarks = detector.detect_landmarks(image)
        cropped_faces = detector.crop_faces(image, faces)

        # 进行综合分析以获得推荐所需的数据
        comprehensive_results = []
        for cropped_face in cropped_faces:
            result = advanced.comprehensive_analysis(cropped_face, landmarks)
            comprehensive_results.append(result)

        # 构建分析结果用于推荐
        analysis_result = {
            "faces": faces,
            "landmarks": landmarks,
            "advanced_analysis": comprehensive_results,
            "age_gender": [{"age": {"age": 25}, "gender": {"gender": "female"}}],  # 模拟数据
            "emotions": [{"emotion": "neutral", "confidence": 0.7}]  # 模拟数据
        }

        # 获取智能推荐
        recommendations = recommendation.comprehensive_recommendations(analysis_result)

        # 记录分析数据
        analytics_data = {
            "type": "smart_recommendations",
            "faces": faces,
            "success": True,
            "processing_time": recommendations.get('processing_time', 0),
            "image_width": image.shape[1],
            "image_height": image.shape[0]
        }
        analytics.record_analysis(analytics_data)

        response = {
            "success": True,
            "message": "智能推荐完成",
            "processing_time": recommendations.get('processing_time', 0),
            "results": recommendations,
            "faces_detected": len(faces)
        }

        return JSONResponse(content=response)

    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"智能推荐失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"推荐失败: {str(e)}")
