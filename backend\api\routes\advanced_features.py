"""
高级功能API路由
Advanced Features API Routes
"""

from fastapi import APIRouter, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
import cv2
import numpy as np
from typing import List, Optional, Dict
import io
from PIL import Image
import base64
import json
import time

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from services.face_detector import FaceDetector
from services.advanced_analysis import AdvancedAnalysis
from services.image_enhancement import ImageEnhancement
from services.analytics_service import AnalyticsService
from utils.logger import app_logger
from utils.config import settings

router = APIRouter()

# 全局服务实例
face_detector = None
advanced_analysis = None
image_enhancement = None
analytics_service = None

def get_services():
    """获取服务实例"""
    global face_detector, advanced_analysis, image_enhancement, analytics_service
    
    if face_detector is None:
        face_detector = FaceDetector()
    if advanced_analysis is None:
        advanced_analysis = AdvancedAnalysis()
    if image_enhancement is None:
        image_enhancement = ImageEnhancement()
    if analytics_service is None:
        analytics_service = AnalyticsService()
    
    return face_detector, advanced_analysis, image_enhancement, analytics_service

def process_uploaded_image(file: UploadFile) -> np.ndarray:
    """处理上传的图像文件"""
    try:
        contents = file.file.read()
        nparr = np.frombuffer(contents, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is None:
            raise ValueError("无法解码图像文件")
        
        return image
        
    except Exception as e:
        app_logger.error(f"处理上传图像失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"图像处理失败: {str(e)}")

def encode_image_to_base64(image: np.ndarray) -> str:
    """将图像编码为base64字符串"""
    try:
        _, buffer = cv2.imencode('.jpg', image)
        image_base64 = base64.b64encode(buffer).decode('utf-8')
        return f"data:image/jpeg;base64,{image_base64}"
    except Exception as e:
        app_logger.error(f"图像编码失败: {str(e)}")
        return ""

@router.post("/advanced/comprehensive-analysis")
async def comprehensive_face_analysis(file: UploadFile = File(...)):
    """
    综合面部分析
    包括对称性、面部形状、肤色、面部特征等高级分析
    """
    start_time = time.time()
    
    try:
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="请上传图像文件")
        
        detector, advanced, _, analytics = get_services()
        
        # 处理图像
        image = process_uploaded_image(file)
        original_image = image.copy()
        
        # 基础检测
        faces = detector.detect_faces(image)
        if not faces:
            return JSONResponse(content={
                "success": False,
                "message": "未检测到人脸",
                "processing_time": time.time() - start_time
            })
        
        landmarks = detector.detect_landmarks(image)
        cropped_faces = detector.crop_faces(image, faces)
        
        # 高级分析
        comprehensive_results = []
        for i, cropped_face in enumerate(cropped_faces):
            result = advanced.comprehensive_analysis(cropped_face, landmarks)
            comprehensive_results.append(result)
        
        # 记录分析数据
        analytics_data = {
            "type": "comprehensive_analysis",
            "faces": faces,
            "success": True,
            "processing_time": time.time() - start_time,
            "image_width": image.shape[1],
            "image_height": image.shape[0]
        }
        analytics.record_analysis(analytics_data)
        
        processing_time = time.time() - start_time
        
        response = {
            "success": True,
            "message": f"综合分析完成，检测到 {len(faces)} 张人脸",
            "processing_time": processing_time,
            "results": {
                "basic_detection": {
                    "faces": faces,
                    "landmarks": landmarks
                },
                "advanced_analysis": comprehensive_results,
                "statistics": {
                    "total_faces": len(faces),
                    "analysis_types": ["symmetry", "face_shape", "skin_tone", "facial_features"]
                }
            }
        }
        
        app_logger.info(f"综合面部分析完成: {len(faces)} 张人脸，耗时 {processing_time:.3f}s")
        
        return JSONResponse(content=response)
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"综合面部分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")

@router.post("/advanced/beauty-filter")
async def apply_beauty_filter(
    file: UploadFile = File(...),
    settings: str = Form(default='{}')
):
    """
    应用美颜滤镜
    """
    start_time = time.time()
    
    try:
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="请上传图像文件")
        
        detector, _, enhancer, analytics = get_services()
        
        # 解析美颜设置
        try:
            beauty_settings = json.loads(settings)
        except:
            beauty_settings = {
                "skin_smoothing": 0.5,
                "brightness": 10,
                "contrast": 1.1,
                "saturation": 1.2,
                "eye_enhancement": 0.3,
                "teeth_whitening": 0.3,
                "blemish_removal": 0.4
            }
        
        # 处理图像
        image = process_uploaded_image(file)
        
        # 检测人脸和特征点
        faces = detector.detect_faces(image)
        landmarks = detector.detect_landmarks(image)
        
        # 应用美颜滤镜
        beauty_result = enhancer.apply_beauty_filter(image, landmarks, beauty_settings)
        
        if beauty_result["success"]:
            enhanced_image_base64 = encode_image_to_base64(beauty_result["enhanced_image"])
            original_image_base64 = encode_image_to_base64(image)
            
            # 记录分析数据
            analytics_data = {
                "type": "beauty_filter",
                "faces": faces,
                "success": True,
                "processing_time": beauty_result["processing_time"],
                "image_width": image.shape[1],
                "image_height": image.shape[0]
            }
            analytics.record_analysis(analytics_data)
            
            response = {
                "success": True,
                "message": "美颜滤镜应用成功",
                "processing_time": beauty_result["processing_time"],
                "settings_applied": beauty_result["settings_applied"],
                "images": {
                    "original": original_image_base64,
                    "enhanced": enhanced_image_base64
                },
                "faces_detected": len(faces)
            }
        else:
            response = {
                "success": False,
                "message": "美颜滤镜应用失败",
                "error": beauty_result.get("error", "未知错误"),
                "processing_time": beauty_result["processing_time"]
            }
        
        return JSONResponse(content=response)
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"美颜滤镜应用失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")

@router.get("/analytics/daily-stats")
async def get_daily_statistics(days: int = 7):
    """获取每日统计数据"""
    try:
        _, _, _, analytics = get_services()
        stats = analytics.get_daily_statistics(days)
        
        return JSONResponse(content={
            "success": True,
            "data": stats
        })
        
    except Exception as e:
        app_logger.error(f"获取每日统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")

@router.get("/analytics/emotion-stats")
async def get_emotion_statistics(days: int = 30):
    """获取表情统计数据"""
    try:
        _, _, _, analytics = get_services()
        stats = analytics.get_emotion_statistics(days)
        
        return JSONResponse(content={
            "success": True,
            "data": stats
        })
        
    except Exception as e:
        app_logger.error(f"获取表情统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")

@router.get("/analytics/demographic-stats")
async def get_demographic_statistics(days: int = 30):
    """获取人口统计数据"""
    try:
        _, _, _, analytics = get_services()
        stats = analytics.get_demographic_statistics(days)
        
        return JSONResponse(content={
            "success": True,
            "data": stats
        })
        
    except Exception as e:
        app_logger.error(f"获取人口统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")

@router.get("/analytics/performance-stats")
async def get_performance_statistics(days: int = 7):
    """获取性能统计数据"""
    try:
        _, _, _, analytics = get_services()
        stats = analytics.get_performance_statistics(days)
        
        return JSONResponse(content={
            "success": True,
            "data": stats
        })
        
    except Exception as e:
        app_logger.error(f"获取性能统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")

@router.get("/analytics/comprehensive-report")
async def get_comprehensive_report(days: int = 30):
    """获取综合分析报告"""
    try:
        _, _, _, analytics = get_services()
        report = analytics.get_comprehensive_report(days)
        
        return JSONResponse(content={
            "success": True,
            "data": report
        })
        
    except Exception as e:
        app_logger.error(f"获取综合报告失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取报告失败: {str(e)}")

@router.post("/advanced/face-comparison")
async def compare_two_faces(
    file1: UploadFile = File(...),
    file2: UploadFile = File(...)
):
    """
    比较两张人脸的相似度
    """
    start_time = time.time()
    
    try:
        if not file1.content_type.startswith('image/') or not file2.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="请上传图像文件")
        
        detector, advanced, _, analytics = get_services()
        
        # 处理两张图像
        image1 = process_uploaded_image(file1)
        image2 = process_uploaded_image(file2)
        
        # 检测人脸
        faces1 = detector.detect_faces(image1)
        faces2 = detector.detect_faces(image2)
        
        if not faces1 or not faces2:
            return JSONResponse(content={
                "success": False,
                "message": "其中一张或两张图片未检测到人脸",
                "processing_time": time.time() - start_time
            })
        
        # 裁剪人脸
        cropped_faces1 = detector.crop_faces(image1, faces1)
        cropped_faces2 = detector.crop_faces(image2, faces2)
        
        if not cropped_faces1 or not cropped_faces2:
            return JSONResponse(content={
                "success": False,
                "message": "人脸裁剪失败",
                "processing_time": time.time() - start_time
            })
        
        # 简单的相似度计算（基于直方图比较）
        face1 = cropped_faces1[0]
        face2 = cropped_faces2[0]
        
        # 调整大小到相同尺寸
        face1_resized = cv2.resize(face1, (100, 100))
        face2_resized = cv2.resize(face2, (100, 100))
        
        # 计算直方图
        hist1 = cv2.calcHist([face1_resized], [0, 1, 2], None, [50, 50, 50], [0, 256, 0, 256, 0, 256])
        hist2 = cv2.calcHist([face2_resized], [0, 1, 2], None, [50, 50, 50], [0, 256, 0, 256, 0, 256])
        
        # 计算相似度
        similarity = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
        similarity_percentage = max(0, similarity * 100)
        
        # 判断是否为同一人
        is_same_person = similarity_percentage > 70  # 阈值可调整
        
        processing_time = time.time() - start_time
        
        # 记录分析数据
        analytics_data = {
            "type": "face_comparison",
            "faces": faces1 + faces2,
            "success": True,
            "processing_time": processing_time,
            "image_width": max(image1.shape[1], image2.shape[1]),
            "image_height": max(image1.shape[0], image2.shape[0])
        }
        analytics.record_analysis(analytics_data)
        
        response = {
            "success": True,
            "message": "人脸比较完成",
            "processing_time": processing_time,
            "results": {
                "similarity_percentage": round(similarity_percentage, 2),
                "is_same_person": is_same_person,
                "confidence": "high" if similarity_percentage > 80 else "medium" if similarity_percentage > 60 else "low",
                "faces_detected": {
                    "image1": len(faces1),
                    "image2": len(faces2)
                }
            }
        }
        
        return JSONResponse(content=response)
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"人脸比较失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"比较失败: {str(e)}")
