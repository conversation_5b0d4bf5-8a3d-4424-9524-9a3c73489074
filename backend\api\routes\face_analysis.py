"""
人脸分析API路由
Face Analysis API Routes
"""

from fastapi import APIRouter, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
import cv2
import numpy as np
from typing import List, Optional
import io
from PIL import Image
import base64
import time

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from services.face_detector import FaceDetector
from services.emotion_analyzer import EmotionAnalyzer
from services.age_gender_predictor import AgeGenderPredictor
from services.face_recognition_service import FaceRecognitionService
from utils.logger import app_logger
from utils.config import settings

router = APIRouter()

# 全局服务实例（将在main.py中初始化）
face_detector = None
emotion_analyzer = None
age_gender_predictor = None
face_recognition_service = None

def get_services():
    """获取服务实例"""
    global face_detector, emotion_analyzer, age_gender_predictor, face_recognition_service
    
    # 这里应该从main.py获取已初始化的服务实例
    # 为了简化，我们在这里临时创建实例
    if face_detector is None:
        face_detector = FaceDetector()
    if emotion_analyzer is None:
        emotion_analyzer = EmotionAnalyzer()
    if age_gender_predictor is None:
        age_gender_predictor = AgeGenderPredictor()
    if face_recognition_service is None:
        face_recognition_service = FaceRecognitionService()
    
    return face_detector, emotion_analyzer, age_gender_predictor, face_recognition_service

def process_uploaded_image(file: UploadFile) -> np.ndarray:
    """处理上传的图像文件"""
    try:
        # 读取文件内容
        contents = file.file.read()
        
        # 转换为numpy数组
        nparr = np.frombuffer(contents, np.uint8)
        
        # 解码图像
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is None:
            raise ValueError("无法解码图像文件")
        
        return image
        
    except Exception as e:
        app_logger.error(f"处理上传图像失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"图像处理失败: {str(e)}")

def encode_image_to_base64(image: np.ndarray) -> str:
    """将图像编码为base64字符串"""
    try:
        _, buffer = cv2.imencode('.jpg', image)
        image_base64 = base64.b64encode(buffer).decode('utf-8')
        return f"data:image/jpeg;base64,{image_base64}"
    except Exception as e:
        app_logger.error(f"图像编码失败: {str(e)}")
        return ""

@router.post("/analyze")
async def basic_face_analysis(file: UploadFile = File(...)):
    """
    基础人脸分析 (简化版)
    """
    return await complete_face_analysis(file)

@router.post("/analyze/complete")
async def complete_face_analysis(file: UploadFile = File(...)):
    """
    完整的人脸分析
    包括人脸检测、表情分析、年龄性别预测、特征点检测
    """
    start_time = time.time()
    
    try:
        # 验证文件类型
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="请上传图像文件")
        
        # 获取服务实例
        detector, emotion_analyzer_svc, age_gender_svc, recognition_svc = get_services()
        
        # 处理上传的图像
        image = process_uploaded_image(file)
        original_image = image.copy()
        
        # 1. 人脸检测
        faces = detector.detect_faces(image)
        
        if not faces:
            return JSONResponse(content={
                "success": False,
                "message": "未检测到人脸",
                "processing_time": time.time() - start_time
            })
        
        # 2. 特征点检测
        landmarks = detector.detect_landmarks(image)
        
        # 3. 裁剪人脸用于后续分析
        cropped_faces = detector.crop_faces(image, faces)
        
        # 4. 表情分析
        emotion_results = []
        if cropped_faces:
            emotion_results = emotion_analyzer_svc.analyze_emotions_batch(cropped_faces)
        
        # 5. 年龄性别预测
        age_gender_results = []
        if cropped_faces:
            age_gender_results = age_gender_svc.predict_batch(cropped_faces)
        
        # 6. 人脸识别
        recognition_results = []
        for cropped_face in cropped_faces:
            recognition_result = recognition_svc.recognize_face(cropped_face)
            recognition_results.append(recognition_result)
        
        # 7. 绘制结果图像
        result_image = original_image.copy()
        
        for i, face in enumerate(faces):
            bbox = face['bbox']
            
            # 绘制人脸框
            cv2.rectangle(
                result_image,
                (bbox['x'], bbox['y']),
                (bbox['x'] + bbox['width'], bbox['y'] + bbox['height']),
                (0, 255, 0), 2
            )
            
            # 绘制分析结果
            y_offset = bbox['y'] - 10
            
            # 表情
            if i < len(emotion_results):
                emotion = emotion_results[i].get('emotion_cn', '未知')
                emotion_conf = emotion_results[i].get('confidence', 0.0)
                cv2.putText(result_image, f"表情: {emotion} ({emotion_conf:.2f})", 
                           (bbox['x'], y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                y_offset -= 20
            
            # 年龄性别
            if i < len(age_gender_results):
                age_range = age_gender_results[i]['age'].get('age_range', '未知')
                gender_cn = age_gender_results[i]['gender'].get('gender_cn', '未知')
                cv2.putText(result_image, f"年龄: {age_range}, 性别: {gender_cn}", 
                           (bbox['x'], y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
                y_offset -= 20
            
            # 身份识别
            if i < len(recognition_results):
                if recognition_results[i]['recognized']:
                    name = recognition_results[i]['name']
                    conf = recognition_results[i]['confidence']
                    cv2.putText(result_image, f"身份: {name} ({conf:.2f})", 
                               (bbox['x'], y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
        
        # 绘制特征点
        if landmarks:
            for face_landmarks in landmarks:
                for point in face_landmarks['landmarks']:
                    cv2.circle(result_image, (point['x'], point['y']), 1, (255, 0, 0), -1)
        
        # 编码结果图像
        result_image_base64 = encode_image_to_base64(result_image)
        
        processing_time = time.time() - start_time
        
        # 构建响应
        response = {
            "success": True,
            "message": f"成功分析 {len(faces)} 张人脸",
            "processing_time": processing_time,
            "results": {
                "faces": faces,
                "landmarks": landmarks,
                "emotions": emotion_results,
                "age_gender": age_gender_results,
                "recognition": recognition_results,
                "statistics": {
                    "total_faces": len(faces),
                    "total_landmarks": sum(len(f['landmarks']) for f in landmarks) if landmarks else 0,
                    "emotions_detected": len([e for e in emotion_results if e.get('emotion') != 'error']),
                    "recognized_faces": len([r for r in recognition_results if r.get('recognized')])
                }
            },
            "images": {
                "result": result_image_base64
            }
        }
        
        app_logger.info(f"完整人脸分析完成: {len(faces)} 张人脸，耗时 {processing_time:.3f}s")
        
        return JSONResponse(content=response)
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"完整人脸分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")

@router.post("/detect")
async def detect_faces(file: UploadFile = File(...)):
    """仅进行人脸检测"""
    try:
        detector, _, _, _ = get_services()
        image = process_uploaded_image(file)
        
        faces = detector.detect_faces(image)
        result_image = detector.draw_detections(image, faces)
        result_image_base64 = encode_image_to_base64(result_image)
        
        return JSONResponse(content={
            "success": True,
            "faces": faces,
            "total_faces": len(faces),
            "result_image": result_image_base64
        })
        
    except Exception as e:
        app_logger.error(f"人脸检测失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"检测失败: {str(e)}")

@router.post("/emotion")
async def analyze_emotion(file: UploadFile = File(...)):
    """表情分析"""
    try:
        detector, emotion_analyzer_svc, _, _ = get_services()
        image = process_uploaded_image(file)
        
        faces = detector.detect_faces(image)
        if not faces:
            raise HTTPException(status_code=400, detail="未检测到人脸")
        
        cropped_faces = detector.crop_faces(image, faces)
        emotion_results = emotion_analyzer_svc.analyze_emotions_batch(cropped_faces)
        
        return JSONResponse(content={
            "success": True,
            "emotions": emotion_results,
            "total_faces": len(faces)
        })
        
    except Exception as e:
        app_logger.error(f"表情分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")

@router.post("/age-gender")
async def predict_age_gender(file: UploadFile = File(...)):
    """年龄性别预测"""
    try:
        detector, _, age_gender_svc, _ = get_services()
        image = process_uploaded_image(file)
        
        faces = detector.detect_faces(image)
        if not faces:
            raise HTTPException(status_code=400, detail="未检测到人脸")
        
        cropped_faces = detector.crop_faces(image, faces)
        age_gender_results = age_gender_svc.predict_batch(cropped_faces)
        
        return JSONResponse(content={
            "success": True,
            "age_gender": age_gender_results,
            "total_faces": len(faces)
        })
        
    except Exception as e:
        app_logger.error(f"年龄性别预测失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"预测失败: {str(e)}")

@router.post("/recognize")
async def recognize_faces(file: UploadFile = File(...)):
    """人脸识别"""
    try:
        detector, _, _, recognition_svc = get_services()
        image = process_uploaded_image(file)
        
        faces = detector.detect_faces(image)
        if not faces:
            raise HTTPException(status_code=400, detail="未检测到人脸")
        
        cropped_faces = detector.crop_faces(image, faces)
        recognition_results = []
        
        for cropped_face in cropped_faces:
            result = recognition_svc.recognize_face(cropped_face)
            recognition_results.append(result)
        
        return JSONResponse(content={
            "success": True,
            "recognition": recognition_results,
            "total_faces": len(faces)
        })
        
    except Exception as e:
        app_logger.error(f"人脸识别失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"识别失败: {str(e)}")

@router.post("/add-face")
async def add_known_face(
    file: UploadFile = File(...),
    name: str = Form(...)
):
    """添加已知人脸到数据库"""
    try:
        _, _, _, recognition_svc = get_services()
        image = process_uploaded_image(file)
        
        success = recognition_svc.add_known_face(image, name)
        
        if success:
            return JSONResponse(content={
                "success": True,
                "message": f"成功添加人脸: {name}"
            })
        else:
            raise HTTPException(status_code=400, detail="添加人脸失败")
        
    except Exception as e:
        app_logger.error(f"添加人脸失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"添加失败: {str(e)}")

@router.get("/database-info")
async def get_database_info():
    """获取人脸数据库信息"""
    try:
        _, _, _, recognition_svc = get_services()
        info = recognition_svc.get_database_info()
        
        return JSONResponse(content={
            "success": True,
            "database_info": info
        })
        
    except Exception as e:
        app_logger.error(f"获取数据库信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取信息失败: {str(e)}")
