"""
日志配置模块
Logging Configuration Module
"""

import sys
import os
from pathlib import Path
from loguru import logger
from .config import settings

def setup_logger():
    """设置日志配置"""
    
    # 移除默认的日志处理器
    logger.remove()
    
    # 确保日志目录存在
    log_dir = Path(settings.LOG_FILE).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 控制台日志格式
    console_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 文件日志格式
    file_format = (
        "{time:YYYY-MM-DD HH:mm:ss} | "
        "{level: <8} | "
        "{name}:{function}:{line} | "
        "{message}"
    )
    
    # 添加控制台处理器
    logger.add(
        sys.stdout,
        format=console_format,
        level=settings.LOG_LEVEL,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 添加文件处理器
    logger.add(
        settings.LOG_FILE,
        format=file_format,
        level=settings.LOG_LEVEL,
        rotation="10 MB",
        retention="30 days",
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    # 添加错误日志文件
    error_log_file = str(Path(settings.LOG_FILE).parent / "error.log")
    logger.add(
        error_log_file,
        format=file_format,
        level="ERROR",
        rotation="10 MB",
        retention="30 days",
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    return logger

# 创建日志实例
app_logger = setup_logger()

# 导出日志函数
def log_info(message: str, **kwargs):
    """记录信息日志"""
    app_logger.info(message, **kwargs)

def log_warning(message: str, **kwargs):
    """记录警告日志"""
    app_logger.warning(message, **kwargs)

def log_error(message: str, **kwargs):
    """记录错误日志"""
    app_logger.error(message, **kwargs)

def log_debug(message: str, **kwargs):
    """记录调试日志"""
    app_logger.debug(message, **kwargs)

def log_critical(message: str, **kwargs):
    """记录严重错误日志"""
    app_logger.critical(message, **kwargs)
